import 'app_logger.dart';
import 'user_storage.dart';

/// 用户上下文管理器 - 简化版本
class UserContext {
  static UserContext? _instance;
  String? _currentUserPhone;

  UserContext._();

  /// 获取单例实例
  static UserContext get instance {
    _instance ??= UserContext._();
    return _instance!;
  }

  /// 获取当前用户手机号
  String? get currentUserPhone => _currentUserPhone;

  /// 检查是否有用户上下文
  bool get hasUserContext => _currentUserPhone != null && _currentUserPhone!.isNotEmpty;

  /// 设置当前用户
  void setCurrentUser(String? userPhone) {
    _currentUserPhone = userPhone;
    AppLogger.info('用户上下文已更新: ${userPhone ?? "已清除"}');
  }

  /// 生成用户相关的存储键名
  String getUserKey(String baseKey) {
    return UserStorage.getUserKey(baseKey, _currentUserPhone);
  }

  /// 清理当前用户数据
  Future<void> clearCurrentUserData() async {
    if (!hasUserContext) return;
    
    try {
      await UserStorage.clearUserData(_currentUserPhone!);
      AppLogger.info('当前用户数据已清理: $_currentUserPhone');
    } catch (e, stackTrace) {
      AppLogger.error('清理当前用户数据失败: $e', error: e, stackTrace: stackTrace);
    }
  }

  /// 迁移现有数据到当前用户
  Future<void> migrateExistingDataToCurrentUser() async {
    if (!hasUserContext) return;
    
    try {
      await UserStorage.migrateDataToUser(_currentUserPhone!);
      AppLogger.info('数据已迁移到当前用户: $_currentUserPhone');
    } catch (e, stackTrace) {
      AppLogger.error('数据迁移失败: $e', error: e, stackTrace: stackTrace);
    }
  }

  /// 切换用户
  Future<void> switchUser(String newUserPhone, {bool clearCurrentData = true}) async {
    final oldUserPhone = _currentUserPhone;
    
    try {
      if (clearCurrentData && hasUserContext) {
        await clearCurrentUserData();
      }
      
      setCurrentUser(newUserPhone);
      await migrateExistingDataToCurrentUser();
      
      AppLogger.info('用户切换完成: ${oldUserPhone ?? "无"} -> $newUserPhone');
    } catch (e, stackTrace) {
      AppLogger.error('用户切换失败: $e', error: e, stackTrace: stackTrace);
      _currentUserPhone = oldUserPhone;
      rethrow;
    }
  }

  /// 检查当前用户是否有数据
  Future<bool> hasCurrentUserData() async {
    if (!hasUserContext) return false;
    return await UserStorage.hasUserData(_currentUserPhone!);
  }

  /// 重置（用于测试）
  void reset() {
    _currentUserPhone = null;
    AppLogger.info('用户上下文已重置');
  }
}
