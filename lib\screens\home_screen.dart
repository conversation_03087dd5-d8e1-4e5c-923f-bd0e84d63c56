import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/device_provider.dart';
import '../screens/health_screen.dart';
import '../screens/device_control_screen.dart';
import '../screens/device_manage_screen.dart';
import '../screens/profile_screen.dart';

class HomeScreen extends StatefulWidget {
  @override
  _HomeScreenState createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _currentIndex = 0;
  final List<Widget> _children = [
    HealthScreen(),
    DeviceControlScreen(),
    DeviceManageScreen(),
    ProfileScreen(),
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      final deviceProvider =
          Provider.of<DeviceProvider>(context, listen: false);
      deviceProvider.initDevices(true);
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // 只在首次加载或有新参数时更新索引
    final args = ModalRoute.of(context)?.settings.arguments as int?;
    if (args != null && args != _currentIndex) {
      setState(() {
        _currentIndex = args;
      });
    }
  }

  void onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    // 加载设备的属性
    return Scaffold(
      body: _children[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        onTap: onTabTapped, // new
        currentIndex:
            _currentIndex, // this will be set when a new tab is tapped
        fixedColor: const Color.fromARGB(255, 243, 117, 33),
        type: BottomNavigationBarType.fixed,
        iconSize: 20.0,
        backgroundColor: const Color.fromARGB(237, 231, 231, 230),

        items: [
          BottomNavigationBarItem(
            icon: new Icon(Icons.monitor_heart_rounded),
            label: '健康',
          ),
          BottomNavigationBarItem(
            icon: new Icon(Icons.security),
            label: '守护',
          ),
          BottomNavigationBarItem(
            icon: new Icon(Icons.watch),
            label: '设备',
          ),
          BottomNavigationBarItem(
            icon: new Icon(Icons.person),
            label: '我的',
          ),
        ],
      ),
    );
  }
}
