import 'package:flutter/material.dart';

class UltrasonicSlider extends StatelessWidget {
  final String title;
  final double value;
  final double min;
  final double max;
  final double step;
  final String unit;
  final Function(double) onChanged;
  final bool enabled;

  const UltrasonicSlider({
    Key? key,
    required this.title,
    required this.value,
    required this.min,
    required this.max,
    required this.step,
    required this.unit,
    required this.onChanged,
    this.enabled = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Icon(
                  title == '频率' ? Icons.graphic_eq : Icons.timer,
                  size: 18,
                  color: enabled
                      ? (title == '频率' ? Colors.blue[600] : Colors.orange[600])
                      : Colors.grey,
                ),
                SizedBox(width: 6),
                Text(
                  '$title:',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                    color: enabled ? Colors.black87 : Colors.grey,
                  ),
                ),
              ],
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: enabled
                    ? (title == '频率' ? Colors.blue[50] : Colors.orange[50])
                    : Colors.grey[100],
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: enabled
                      ? (title == '频率'
                          ? Colors.blue[200]!
                          : Colors.orange[200]!)
                      : Colors.grey[300]!,
                  width: 1,
                ),
              ),
              child: Text(
                '${value.toStringAsFixed(unit == 'kHz' ? 0 : 1)} $unit',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: enabled
                      ? (title == '频率' ? Colors.blue[700] : Colors.orange[700])
                      : Colors.grey,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 12),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: enabled
                ? (title == '频率' ? Colors.blue[600] : Colors.orange[600])
                : Colors.grey,
            inactiveTrackColor: enabled
                ? (title == '频率' ? Colors.blue[200] : Colors.orange[200])
                : Colors.grey.withOpacity(0.2),
            thumbColor: enabled
                ? (title == '频率' ? Colors.blue[600] : Colors.orange[600])
                : Colors.grey,
            thumbShape: RoundSliderThumbShape(enabledThumbRadius: 12.0),
            overlayShape: RoundSliderOverlayShape(overlayRadius: 24.0),
            overlayColor: enabled
                ? (title == '频率'
                    ? Colors.blue[600]!.withOpacity(0.1)
                    : Colors.orange[600]!.withOpacity(0.1))
                : Colors.grey.withOpacity(0.1),
            trackHeight: 6.0,
            valueIndicatorShape: PaddleSliderValueIndicatorShape(),
            valueIndicatorColor: enabled
                ? (title == '频率' ? Colors.blue[600] : Colors.orange[600])
                : Colors.grey,
            valueIndicatorTextStyle: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
            showValueIndicator: ShowValueIndicator.onlyForDiscrete,
          ),
          child: Slider(
            value: value,
            min: min,
            max: max,
            divisions: ((max - min) / step).round(),
            label: '${value.toStringAsFixed(unit == 'kHz' ? 0 : 1)} $unit',
            onChanged: enabled
                ? (newValue) {
                    // 根据步长调整值
                    double adjustedValue = (newValue / step).round() * step;
                    onChanged(adjustedValue);
                  }
                : null,
          ),
        ),
        SizedBox(height: 4),
        // 显示范围信息
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${min.toStringAsFixed(unit == 'kHz' ? 0 : 1)} $unit',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
            Text(
              '${max.toStringAsFixed(unit == 'kHz' ? 0 : 1)} $unit',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
