import 'package:flutter/material.dart';

/// 动画时间显示组件
///
/// 实现字符级别的局部更新，消除时间显示残影
///
/// 特性：
/// - 每个数字独立更新，只有变化的数字会触发动画
/// - 使用AnimatedSwitcher提供流畅的切换效果
/// - 完全消除时间更新时的视觉残影
/// - 性能优化：只重建变化的字符
class AnimatedTimeDisplay extends StatelessWidget {
  final String timeString;
  final TextStyle? textStyle;
  final Duration animationDuration;
  final Curve animationCurve;

  const AnimatedTimeDisplay({
    Key? key,
    required this.timeString,
    this.textStyle,
    this.animationDuration = const Duration(milliseconds: 200),
    this.animationCurve = Curves.easeInOut,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 解析时间字符串，格式为 "SS:MS"（秒:毫秒）
    final timeParts = _parseTimeString(timeString);

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.baseline, // 确保所有字符基线对齐
      textBaseline: TextBaseline.alphabetic,
      children: [
        // 秒钟十位
        _buildAnimatedDigit(timeParts.secondTens),
        // 秒钟个位
        _buildAnimatedDigit(timeParts.secondOnes),
        // 冒号（不变化，直接显示）
        Text(
          ':',
          style: textStyle,
        ),
        // 毫秒十分位
        _buildAnimatedDigit(timeParts.millisecondTens),
        // 毫秒百分位
        _buildAnimatedDigit(timeParts.millisecondOnes),
      ],
    );
  }

  /// 构建单个数字的动画切换组件
  Widget _buildAnimatedDigit(int digit) {
    return AnimatedSwitcher(
      duration: animationDuration,
      switchInCurve: animationCurve,
      switchOutCurve: animationCurve,
      transitionBuilder: (Widget child, Animation<double> animation) {
        // 使用纯淡入淡出效果，避免位置偏移导致的对齐问题
        return FadeTransition(
          opacity: animation,
          child: child,
        );
      },
      child: SizedBox(
        // 固定宽度确保数字切换时不会影响整体布局
        width: _getDigitWidth(),
        child: Text(
          '$digit',
          key: ValueKey<int>(digit), // 使用数字作为key，确保动画正确触发
          style: textStyle,
          textAlign: TextAlign.center, // 居中对齐
        ),
      ),
    );
  }

  /// 获取数字显示的固定宽度
  double _getDigitWidth() {
    // 根据字体大小计算合适的宽度，确保所有数字都能正常显示
    final fontSize = textStyle?.fontSize ?? 14.0;
    return fontSize * 0.7; // 经验值，可以容纳所有数字字符
  }

  /// 解析时间字符串为各个数字位
  TimeParts _parseTimeString(String timeString) {
    // 格式为 "SS:MS"（秒:毫秒），如"10:98"表示10秒980毫秒
    if (timeString.length >= 5 && timeString.contains(':')) {
      final parts = timeString.split(':');
      if (parts.length >= 2) {
        final seconds = int.tryParse(parts[0]) ?? 0;
        final milliseconds = int.tryParse(parts[1]) ?? 0;

        return TimeParts(
          secondTens: seconds ~/ 10,
          secondOnes: seconds % 10,
          millisecondTens: milliseconds ~/ 10,
          millisecondOnes: milliseconds % 10,
        );
      }
    }

    // 解析失败时返回默认值
    return TimeParts(
      secondTens: 0,
      secondOnes: 0,
      millisecondTens: 0,
      millisecondOnes: 0,
    );
  }
}

/// 时间各个位的数据结构（秒:毫秒格式）
class TimeParts {
  final int secondTens; // 秒钟十位
  final int secondOnes; // 秒钟个位
  final int millisecondTens; // 毫秒十分位
  final int millisecondOnes; // 毫秒百分位

  const TimeParts({
    required this.secondTens,
    required this.secondOnes,
    required this.millisecondTens,
    required this.millisecondOnes,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is TimeParts &&
          runtimeType == other.runtimeType &&
          secondTens == other.secondTens &&
          secondOnes == other.secondOnes &&
          millisecondTens == other.millisecondTens &&
          millisecondOnes == other.millisecondOnes;

  @override
  int get hashCode =>
      secondTens.hashCode ^
      secondOnes.hashCode ^
      millisecondTens.hashCode ^
      millisecondOnes.hashCode;

  @override
  String toString() {
    return 'TimeParts(${secondTens.toString().padLeft(1, '0')}${secondOnes.toString().padLeft(1, '0')}:${millisecondTens.toString().padLeft(1, '0')}${millisecondOnes.toString().padLeft(1, '0')})';
  }
}
