import 'package:flutter/material.dart';
import '../widgets/error_toast.dart';
import '../constants/constants.dart';

/// 提示工具类
class ToastUtils {
  // 全局GlobalKey，用于获取BuildContext
  static final _navigatorKey = navigatorKey;

  // 获取当前context
  static BuildContext? get _currentContext => _navigatorKey.currentContext;

  /// 显示错误提示
  static void showError(String message) {
    final context = _currentContext;
    if (context != null) {
      ErrorToast.show(context, message);
    } else {
      debugPrint('ToastUtils: 无法显示错误提示，context为空');
    }
  }

  /// 显示错误提示，可自定义显示时间和关闭回调
  static void showErrorWithOptions(
    String message, {
    Duration duration = const Duration(seconds: 5),
    VoidCallback? onDismiss,
  }) {
    final context = _currentContext;
    if (context != null) {
      showDialog(
        context: context,
        barrierDismissible: true,
        builder: (context) => ErrorToast(
          errorMessage: message,
          duration: duration,
          onDismiss: onDismiss,
        ),
      );
    } else {
      debugPrint('ToastUtils: 无法显示错误提示，context为空');
    }
  }

  /// 带context的错误提示，仅在特殊情况下使用
  static void showErrorWithContext(BuildContext context, String message) {
    ErrorToast.show(context, message);
  }
}
