import 'package:flutter/material.dart';
import '../models/pet.dart';
import '../constants/constants.dart';
import '../widgets/pet_info_card.dart';
import '../widgets/no_pet_card.dart';
import '../widgets/pet_loading_card.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  Pet? _pet;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadPetInfo();
  }

  Future<void> _loadPetInfo() async {
    try {
      final pet = await Pet.getPet();
      if (mounted) {
        setState(() {
          _pet = pet;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildPetCard() {
    if (_isLoading) {
      return const PetLoadingCard();
    }

    if (_pet == null) {
      return NoPetCard(onTap: _navigateToAddPet);
    } else {
      return PetInfoCard(
        pet: _pet!,
        onTap: _navigateToPetDetail,
      );
    }
  }

  void _navigateToAddPet() async {
    final result = await Navigator.of(context).pushNamed(Routes.petInfoInput);
    if (result != null) {
      _loadPetInfo(); // 重新加载宠物信息
    }
  }

  void _navigateToPetDetail() async {
    final result = await Navigator.of(context)
        .pushNamed(Routes.petDetail, arguments: _pet);
    if (result != null) {
      _loadPetInfo(); // 重新加载宠物信息
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 当页面重新获得焦点时刷新数据
    _loadPetInfo();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('个人资料'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.orange.shade300,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            const SizedBox(height: 16),
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              alignment: Alignment.centerLeft,
              child: Text(
                '爱宠信息',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const SizedBox(height: 4),
            _buildPetCard(),
            // 可以在这里添加其他个人资料相关的卡片
          ],
        ),
      ),
    );
  }
}
