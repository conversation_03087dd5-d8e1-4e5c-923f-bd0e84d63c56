import '../models/qr_code.dart';
import 'api_service.dart';
import '../utils/toast_utils.dart';
import '../constants/url_form.dart';

class QRCodeService {
  final ApiService _apiService;

  QRCodeService(this._apiService);

  // 获取用户的所有防丢码
  Future<List<QRCode>> getUserQRCodes() async {
    Map<String, dynamic> jsonResponse =
        await _apiService.getRequest(UrlFormat.QrCodeList);

    if (jsonResponse['code'] == 200) {
      final List<dynamic> qrCodesJson = jsonResponse['data'];
      final qrCodesList =
          qrCodesJson.map((json) => QRCode.fromJson(json)).toList();
      return qrCodesList;
    } else if (jsonResponse['code'] == 607) {
      return [];
    } else {
      ToastUtils.showError(jsonResponse['message']);
      throw Exception(jsonResponse['message']);
    }
  }

  // 添加新的防丢码
  Future<QRCode> addQRCode(String url, String contactPhone, String note) async {
    final body = {
      'url': url,
      'contactPhone': contactPhone,
      'note': note,
    };

    final jsonResponse = await _apiService.postRequest(
      UrlFormat.QrCodeBind,
      body,
    );

    if (jsonResponse['code'] == 200) {
      return QRCode.fromJson(jsonResponse['data']);
    } else {
      ToastUtils.showError(' ${jsonResponse['message']}');
      throw Exception(jsonResponse['message']);
    }
  }

  // 删除防丢码
  Future<bool> deleteQRCode(String url) async {
    final body = {
      'url': url,
    };

    final jsonResponse = await _apiService.postRequest(
      UrlFormat.QrCodeUnbind,
      body,
    );

    if (jsonResponse['code'] == 200) {
      return true;
    } else {
      ToastUtils.showError('${jsonResponse['message']}');
      return false;
    }
  }

  // 更新防丢码
  Future<QRCode> updateQRCode(
      String url, String contactPhone, String note) async {
    final body = {
      'url': url,
      'contactPhone': contactPhone,
      'note': note,
    };

    final jsonResponse = await _apiService.postRequest(
      UrlFormat.QrCodeBind,
      body,
    );

    if (jsonResponse['code'] == 200) {
      return QRCode.fromJson(jsonResponse['data']);
    } else {
      ToastUtils.showError('${jsonResponse['message']}');
      throw Exception('${jsonResponse['message']}');
    }
  }
}
