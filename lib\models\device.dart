import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/material.dart';
import '../utils/user_context.dart';

class Device {

  final bool wifiConfig;
  final bool isOnline;
  final bool isSleep; //休眠一定不在线，但是不在线不一定休眠可能是网络或服务器问题
  final bool antiLostMode; // 遛狗防丢模式状态
  final bool nightSleepMode; // 夜间模式状态
  final String bleMac;
  final String deviceName;
  final String productId;
  final String wifiName;
  final int batteryPercent;
  final TimeOfDay sleepStartTime;
  final TimeOfDay sleepEndTime;

  // 必须默认wifi和蓝牙连接的初始值是false
  Device({
    required this.bleMac,
    required this.wifiConfig,
    required this.isOnline,
    required this.deviceName,
    required this.productId,
    required this.isSleep,
    required this.wifiName,
    this.antiLostMode = true, // 默认遛狗防丢开启状态
    this.batteryPercent = 22,
    required this.sleepStartTime,
    required this.sleepEndTime,
    this.nightSleepMode = true, // 默认开启夜间睡眠模式
  });

  factory Device.fromJson(Map<String, dynamic> json) {
    // 处理TimeOfDay对象序列化
    TimeOfDay _parseTimeOfDay(Map<String, dynamic>? timeJson) {
      if (timeJson == null) {
        return TimeOfDay(hour: 0, minute: 0);
      }
      return TimeOfDay(
          hour: timeJson['hour'] ?? 0, minute: timeJson['minute'] ?? 0);
    }

    // 如果睡眠开始时间不存在，默认为晚上12点
    final sleepStartTimeJson = json['sleepStartTime'];
    final sleepStartTime = sleepStartTimeJson is Map<String, dynamic>
        ? _parseTimeOfDay(sleepStartTimeJson)
        : TimeOfDay(hour: 0, minute: 0);

    // 如果睡眠结束时间不存在，默认为早上6点
    final sleepEndTimeJson = json['sleepEndTime'];
    final sleepEndTime = sleepEndTimeJson is Map<String, dynamic>
        ? _parseTimeOfDay(sleepEndTimeJson)
        : TimeOfDay(hour: 6, minute: 0);

    return Device(
      bleMac: json['bleMac'].toString(),
      wifiConfig: json['wifiConfig'] ?? false,
      isOnline: json['isOnline'] ?? true,
      deviceName: json['deviceName'].toString(),
      productId: json['productId']?.toString() ?? '10000001',
      isSleep: json['isSleep'] ?? false,
      wifiName: json['wifiName'].toString(),
      antiLostMode: json['antiLostMode'] ?? true,
      batteryPercent: json['batteryPercent'] ?? 22,
      sleepStartTime: sleepStartTime,
      sleepEndTime: sleepEndTime,
      nightSleepMode: json['nightSleepMode'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    // 序列化TimeOfDay对象
    Map<String, dynamic> _timeOfDayToJson(TimeOfDay time) {
      return {
        'hour': time.hour,
        'minute': time.minute,
      };
    }

    return {
      'wifiConfig': wifiConfig,
      'isOnline': isOnline,
      'deviceName': deviceName,
      'productId': productId,
      'bleMac': bleMac,
      'wifiName': wifiName,
      'antiLostMode': antiLostMode,
      'isSleep': isSleep,
      'batteryPercent': batteryPercent,
      'sleepStartTime': _timeOfDayToJson(sleepStartTime),
      'sleepEndTime': _timeOfDayToJson(sleepEndTime),
      'nightSleepMode': nightSleepMode,
    };
  }

  static Future<void> saveDevice(Device device,
      {String key = 'petDevice'}) async {
    final asyncPrefs = SharedPreferencesAsync();
    final storageKey = UserContext.instance.getUserKey(key);
    String jsonString = jsonEncode(device.toJson());
    await asyncPrefs.setString(storageKey, jsonString);
  }

  static Future<Device?> getDevice({String key = 'petDevice'}) async {
    final asyncPrefs = SharedPreferencesAsync();
    final storageKey = UserContext.instance.getUserKey(key);
    String? jsonString = await asyncPrefs.getString(storageKey);
    if (jsonString != null) {
      return Device.fromJson(jsonDecode(jsonString));
    }
    return null;
  }

  static Future<void> removeDevice({String key = 'petDevice'}) async {
    final asyncPrefs = SharedPreferencesAsync();
    final storageKey = UserContext.instance.getUserKey(key);
    await asyncPrefs.remove(storageKey);
  }
}
