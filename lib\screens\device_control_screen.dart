import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../providers/device_provider.dart';
import '../constants/constants.dart';
import '../utils/app_storage.dart';
import '../services/device_service.dart';

class DeviceControlScreen extends StatefulWidget {
  const DeviceControlScreen({Key? key}) : super(key: key);

  @override
  State<DeviceControlScreen> createState() => _DeviceControlScreenState();
}

class _DeviceControlScreenState extends State<DeviceControlScreen> {
  bool _isMarqueeLightOn = false;
  bool _isAntiLostModeOn = false;
  bool _isCalling = false;

  // 设备控制服务
  late DeviceControlService _deviceControlService;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 获取设备控制服务
      _deviceControlService =
          Provider.of<DeviceControlService>(context, listen: false);
      // 从DeviceProvider获取防丢模式状态
      final deviceProvider =
          Provider.of<DeviceProvider>(context, listen: false);
      if (deviceProvider.device != null) {
        setState(() {
          _isAntiLostModeOn = deviceProvider.isAntiLostModeEnabled;
        });
      }

      _loadSavedStates();
      _checkDeviceAvailability();
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('设备控制'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Consumer<DeviceProvider>(
          builder: (context, deviceProvider, child) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildControlCard(
                  title: '超声驱虫',
                  subtitle: '发射超声波驱赶虫子',
                  icon: Icons.pest_control,
                  actionWidget: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: deviceProvider.isRepellentActive
                          ? Colors.red
                          : Colors.orangeAccent,
                    ),
                    onPressed: () => _checkDeviceBeforeAction(
                        deviceProvider.isRepellentActive
                            ? _showStopRepellentDialog
                            : _ultrasonicRepellent),
                    child: Text(
                      deviceProvider.isRepellentActive ? '停止' : '启动',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                _buildControlCard(
                  title: '跑马灯',
                  subtitle: '开启设备灯光效果',
                  icon: Icons.lightbulb,
                  actionWidget: Switch(
                    activeColor: Colors.orangeAccent,
                    value: _isMarqueeLightOn,
                    onChanged: (value) => _checkDeviceBeforeAction(
                        (deviceName) => _toggleMarqueeLight(value, deviceName)),
                  ),
                ),
                const SizedBox(height: 16),
                _buildControlCard(
                  title: '呼叫',
                  subtitle: '发出声音呼叫宠物',
                  icon: Icons.notifications_active,
                  actionWidget: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.orangeAccent,
                    ),
                    onPressed: () => _checkDeviceBeforeAction(_callPet),
                    child: const Text(
                      '呼叫',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                _buildControlCard(
                  title: '遛狗防丢模式',
                  subtitle: '宠物远离会发出警报',
                  icon: Icons.pets,
                  actionWidget: Switch(
                    activeColor: Colors.orangeAccent,
                    value: deviceProvider.isAntiLostModeEnabled,
                    onChanged: (value) => _checkDeviceBeforeAction(
                        (deviceName) => _toggleAntiLostMode(value, deviceName)),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  // 检查设备是否可用
  void _checkDeviceAvailability() {
    final deviceProvider = Provider.of<DeviceProvider>(context, listen: false);
    if (deviceProvider.device == null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _showAddDeviceDialog();
      });
    }
  }

  // 执行操作前检查设备
  void _checkDeviceBeforeAction(Function action) {
    final deviceProvider = Provider.of<DeviceProvider>(context, listen: false);
    final deviceName = deviceProvider.device!.deviceName;
    if (deviceProvider.device == null) {
      _showAddDeviceDialog();
    } else {
      action(deviceName);
    }
  }

  // 显示添加设备对话框
  void _showAddDeviceDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('提示'),
        content: const Text('您还没有添加设备，请先添加设备'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pushNamed(context, Routes.deviceScan);
            },
            child: const Text('去添加'),
          ),
        ],
      ),
    );
  }

  // 确认停止超声驱虫
  void _showStopRepellentDialog(String deviceName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('提示'),
        content: const Text('正在进行超声驱虫，是否停止？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _stopRepellent();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  // 超声驱虫功能
  void _ultrasonicRepellent(String deviceName) async {
    final deviceProvider = Provider.of<DeviceProvider>(context, listen: false);
    await deviceProvider.startRepellent();
  }

  // 停止超声驱虫
  void _stopRepellent() async {
    final deviceProvider = Provider.of<DeviceProvider>(context, listen: false);
    await deviceProvider.stopRepellent();
  }

  // 呼叫功能
  void _callPet(String deviceName) async {
    // 先后呼叫5秒，再关闭呼叫
    // 关闭呼叫会关闭当前设备防丢模式
    if (_isCalling) {
      return;
    }
    await _deviceControlService.call(
      deviceName: deviceName,
      command: 'on',
    );
    setState(() {
      _isCalling = true;
    });
    await Future.delayed(const Duration(seconds: 5));
    await _deviceControlService.call(
      deviceName: deviceName,
      command: 'off',
    );
    setState(() {
      _isCalling = false;
    });

    if (_isAntiLostModeOn) {
      await _deviceControlService.antiLostMode(
        deviceName: deviceName,
        command: 'on',
      );
    }
  }

  // 切换跑马灯状态
  void _toggleMarqueeLight(bool value, String deviceName) async {
    // 先更新UI
    setState(() {
      _isMarqueeLightOn = value;
    });

    // 调用API
    bool success = await _deviceControlService.marqueeLight(
      deviceName: deviceName,
      command: value ? 'on' : 'off',
    );

    // 如果API调用失败，恢复原来的状态
    if (!success) {
      setState(() {
        _isMarqueeLightOn = !value;
      });
      return;
    }

    // 保存状态
    _saveState('marquee_light', value);
  }

  // 切换遛狗防丢模式状态
  void _toggleAntiLostMode(bool value, String deviceName) async {
    final deviceProvider = Provider.of<DeviceProvider>(context, listen: false);

    // 使用DeviceProvider设置防丢模式
    bool success = await deviceProvider.setAntiLostMode(value);

    // 如果API调用失败，恢复原来的状态
    if (!success) {
      setState(() {
        _isAntiLostModeOn = !value;
      });
      return;
    }

    // 显示操作结果提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(value ? '遛狗防丢模式已开启' : '遛狗防丢模式已关闭')),
    );
  }

  // 加载保存的开关状态
  Future<void> _loadSavedStates() async {
    final isMarqueeLightOn = await AppStorage.getUserBool('marquee_light');
    setState(() {
      _isMarqueeLightOn = isMarqueeLightOn;
    });
  }

  // 保存开关状态
  Future<void> _saveState(String key, bool value) async {
    await AppStorage.setUserBool(key, value);
  }

  Widget _buildControlCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Widget actionWidget,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            Icon(
              icon,
              size: 40,
              color: Colors.orangeAccent,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            actionWidget,
          ],
        ),
      ),
    );
  }
}
