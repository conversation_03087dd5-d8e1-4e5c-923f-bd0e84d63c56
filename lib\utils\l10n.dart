import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
// import 'package:flutter_localizations/flutter_localizations.dart';

// L10n 类，用于国际化和本地化支持
class L10n {
  static final all = [
    const Locale('en'), // 英文
    const Locale('zh'), // 中文
    // 添加更多支持的语言
  ];

  // 根据语言代码获取显示名称
  static String getLanguageName(String code) {
    switch (code) {
      case 'en':
        return 'English';
      case 'zh':
        return '中文';
      // 添加更多语言
      default:
        return 'Unknown';
    }
  }

  // 初始化本地化设置
  static void init(BuildContext context) {
    // 设置默认的本地化
    Intl.defaultLocale = Localizations.localeOf(context).languageCode;
  }
}