import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:permission_handler/permission_handler.dart';

import '../providers/qr_code_provider.dart';
import '../services/qr_code_service.dart';
import '../services/api_service.dart';
import '../widgets/qr_code_card.dart';
import '../widgets/qr_scanner.dart';
import '../widgets/add_qr_code_dialog.dart';
import '../widgets/edit_qr_code_dialog.dart';

class QRScreen extends StatefulWidget {
  const QRScreen({Key? key}) : super(key: key);

  @override
  State<QRScreen> createState() => _QRScreenState();
}

class _QRScreenState extends State<QRScreen> {
  bool _isScanning = false;
  bool _isInitialLoading = true; // 用于跟踪初始加载状态
  bool _isInited = false;

  @override
  void initState() {
    super.initState();
    // 初始化后加载QR码列表
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadQRCodes();
    });
  }

  Future<void> _loadQRCodes() async {
    setState(() {
      _isInitialLoading = true;
    });

    final provider = Provider.of<QRCodeProvider>(context, listen: false);
    final result = await provider.fetchQRCodes();

    if (mounted) {
      setState(() {
        _isInitialLoading = false;
        _isInited = result;
      });
    }
  }

  Future<void> _startScan() async {
    // 请求相机权限
    final status = await Permission.camera.request();
    if (status.isGranted) {
      setState(() {
        _isScanning = true;
      });
    } else {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('需要相机权限才能扫描二维码'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _onQRCodeDetected(String url) {
    setState(() {
      _isScanning = false;
    });

    // 显示添加QR码对话框
    showDialog(
      context: context,
      builder: (dialogContext) => AddQRCodeDialog(
        url: url,
        onAdd: (url, contactPhone, note) async {
          final provider = Provider.of<QRCodeProvider>(context, listen: false);
          await provider.addQRCode(url, contactPhone, note);
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('防丢码绑定'),
      ),
      body: _isScanning
          ? QRScanner(onDetect: _onQRCodeDetected)
          : Consumer<QRCodeProvider>(
              builder: (context, provider, child) {
                // 仅在初始加载时显示加载指示器
                if (_isInitialLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                // 只有在初始加载出错时才显示重试按钮
                if (provider.error != null && !_isInited) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadQRCodes,
                          child: const Text('重试'),
                        ),
                      ],
                    ),
                  );
                }

                if (!provider.hasQRCodes) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.qr_code,
                          size: 100,
                          color: Colors.grey,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          '您还没有绑定防丢码',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          '防丢码可以帮助您找回丢失的宠物',
                          style: TextStyle(color: Colors.grey),
                        ),
                        const SizedBox(height: 24),
                        ElevatedButton.icon(
                          onPressed: _startScan,
                          icon: const Icon(Icons.add),
                          label: const Text('添加防丢码'),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                }

                return Stack(
                  children: [
                    // 如果在进行添加/编辑操作时有加载，则显示加载指示器覆盖层
                    if (provider.isLoading && !_isInitialLoading)
                      Container(
                        color: Colors.black.withOpacity(0.1),
                        child: const Center(
                          child: CircularProgressIndicator(),
                        ),
                      ),

                    ListView.builder(
                      padding: const EdgeInsets.only(bottom: 80),
                      itemCount: provider.qrCodes.length,
                      itemBuilder: (context, index) {
                        final qrCode = provider.qrCodes[index];
                        return QRCodeCard(
                          qrCode: qrCode,
                          onDelete: () async {
                            final confirm = await showDialog<bool>(
                              context: context,
                              builder: (dialogContext) => AlertDialog(
                                title: const Text('确认删除'),
                                content: const Text('您确定要删除此防丢码吗？'),
                                actions: [
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.pop(dialogContext, false),
                                    child: const Text('取消'),
                                  ),
                                  TextButton(
                                    onPressed: () =>
                                        Navigator.pop(dialogContext, true),
                                    child: const Text('删除'),
                                  ),
                                ],
                              ),
                            );

                            if (confirm == true) {
                              await provider.deleteQRCode(qrCode.url);
                            }
                          },
                          onEdit: () {
                            showDialog(
                              context: context,
                              builder: (dialogContext) => EditQRCodeDialog(
                                qrCode: qrCode,
                                onEdit: (url, contactPhone, note) async {
                                  await provider.updateQRCode(
                                    url,
                                    contactPhone,
                                    note,
                                  );
                                },
                              ),
                            );
                          },
                        );
                      },
                    ),
                    Positioned(
                      bottom: 16,
                      left: 0,
                      right: 0,
                      child: Center(
                        child: ElevatedButton.icon(
                          onPressed: _startScan,
                          icon: const Icon(Icons.add),
                          label: const Text('添加防丢码'),
                          style: ElevatedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
    );
  }
}

// 创建QR码屏幕的ChangeNotifierProvider
class QRCodeScreenProvider extends StatelessWidget {
  const QRCodeScreenProvider({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final apiService = ApiService();
    final qrCodeService = QRCodeService(apiService);

    return ChangeNotifierProvider(
      create: (_) => QRCodeProvider(qrCodeService),
      child: const QRScreen(),
    );
  }
}
