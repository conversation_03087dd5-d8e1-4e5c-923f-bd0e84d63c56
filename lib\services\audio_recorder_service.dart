/// 音频录制服务
///
/// 这个服务提供以下功能：
/// 1. 录制音频并实时显示波形
/// 2. 音频播放和波形可视化
/// 3. 支持iOS和Android平台
///
/// 使用方法：
/// ```dart
/// final audioService = AudioRecorderService();
/// await audioService.init();
///
/// // 开始录音
/// await audioService.startRecording();
///
/// // 停止录音
/// await audioService.stopRecording();
///
/// // 播放录音
/// await audioService.playRecording();
///
/// // 清理资源
/// audioService.dispose();
/// ```
///
/// 技术实现：
/// - 使用audio_waveforms包录制音频并提供实时波形显示
/// - 使用audio_waveforms包播放录制的音频
///
/// 配置参数：
/// - 采样率: 16000 Hz
/// - 最大录音时长: 15秒
import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:path_provider/path_provider.dart';
import '../utils/app_logger.dart';

class AudioRecorderService {
  RecorderController _recorderController = RecorderController();
  PlayerController _playerController = PlayerController();
  Timer? _timer;
  String _recordingPath = '';
  bool _isRecording = false;
  bool _hasRecorded = false;
  bool _isPlaying = false;
  List<double> _waveformData = [];
  int _totalRecordingDuration = 0; // 添加总录音时长跟踪

  // 时间显示的ValueNotifier - 用于局部更新时间显示
  final ValueNotifier<String> audioTimeNotifier =
      ValueNotifier<String>("00:00");

  // 状态变化回调 - 用于非时间状态的更新
  VoidCallback? _onStateChanged;

  // 最大录音时长（毫秒）
  static const int maxRecordingDuration = 15000;

  // Getters
  bool get isRecording => _isRecording;
  bool get hasRecorded => _hasRecorded;
  bool get isPlaying => _isPlaying;
  String get audioTime => audioTimeNotifier.value; // 保持向后兼容
  List<double> get waveformData => _waveformData;
  String get recordingPath => _recordingPath;
  RecorderController get recorderController => _recorderController;
  PlayerController get playerController => _playerController;
  int get totalRecordingDuration => _totalRecordingDuration; // 获取总录音时长

  // 设置状态变化回调
  void setStateChangeCallback(VoidCallback callback) {
    _onStateChanged = callback;
  }

  // 同步检查是否存在录音文件（用于初始化时避免界面闪烁）
  Future<bool> hasExistingRecording() async {
    if (_recordingPath.isEmpty) {
      final tempDir = await getTemporaryDirectory();
      _recordingPath = '${tempDir.path}/recording.m4a';
    }
    return await File(_recordingPath).exists();
  }

  // 通知状态变化（非时间相关）
  void _notifyStateChanged() {
    _onStateChanged?.call();
  }

  // 更新时间显示（只更新时间，不触发整体UI重建）
  void _updateAudioTime(String time) {
    audioTimeNotifier.value = time;
  }

  Future<void> init() async {
    final tempDir = await getTemporaryDirectory();
    _recordingPath = '${tempDir.path}/recording.m4a';
    // 检查是否有已存在的录音文件
    if (await File(_recordingPath).exists()) {
      _hasRecorded = true;
      await _preparePlayerController();
      // 尝试获取已存在录音文件的时长
      await _loadExistingRecordingDuration();
    }
    _notifyStateChanged();
    // 检查录音权限
    await _recorderController.checkPermission();
    // 确保目录存在
    if (!await Directory(tempDir.path).exists()) {
      await Directory(tempDir.path).create(recursive: true);
    }
    // 设置音频播放完成监听
    _playerController.onCompletion.listen((_) async {
      _isPlaying = false;
      _timer?.cancel();
      _timer = null;
      // 重置音频时间显示到开始
      _updateAudioTime("00:00");
      _notifyStateChanged(); // 播放状态变化，需要更新UI
    });
    _playerController.setFinishMode(finishMode: FinishMode.pause);
  }

  Future<void> startRecording() async {
    if (_isRecording) return;

    try {
      // 删除之前的录音文件
      final file = File(_recordingPath);
      if (await file.exists()) {
        await file.delete();
      }

      // 重置录音时间
      _updateAudioTime("00:00");
      _hasRecorded = false;

      // 使用audio_waveforms录制（用于波形显示）
      await _recorderController.record(
        path: _recordingPath,
        androidEncoder: AndroidEncoder.aac,
        androidOutputFormat: AndroidOutputFormat.mpeg4,
        iosEncoder: IosEncoder.kAudioFormatMPEG4AAC,
        sampleRate: 16000,
        bitRate: 256000,
      );

      _isRecording = true;
      _notifyStateChanged(); // 录音状态变化，需要更新UI
      int tmpseconds = 0;
      // 启动计时器，每50毫秒更新一次时间（20次/秒），让最后一位数字有变化
      _timer = Timer.periodic(const Duration(milliseconds: 50), (timer) async {
        // 检查是否达到最大录音时长
        if (tmpseconds >= maxRecordingDuration) {
          stopRecording();
          return;
        }
        tmpseconds = tmpseconds + 50;
        // 更新录音时间（只更新时间显示，不重建整个UI）
        // 格式为"秒:毫秒"，如"10:98"表示10秒980毫秒
        final seconds1 = (tmpseconds ~/ 1000).toString().padLeft(2, '0');
        final seconds2 = ((tmpseconds % 1000) ~/ 10).toString().padLeft(2, '0');
        _updateAudioTime("$seconds1:$seconds2");
      });
    } catch (e, stackTrace) {
      AppLogger.warning('录音启动失败: $e', stackTrace: stackTrace);
      _isRecording = false;
      _notifyStateChanged();
    }
  }

  Future<void> stopRecording() async {
    if (!_isRecording) return;

    try {
      // 停止录音
      await _recorderController.stop();

      _isRecording = false;
      _hasRecorded = true;

      if (_timer != null) {
        // 记录总录音时长
        final timeString = audioTimeNotifier.value;
        final parts = timeString.split(':');
        if (parts.length == 2) {
          final seconds = int.parse(parts[0]);
          final centiseconds = int.parse(parts[1]);
          _totalRecordingDuration = seconds * 1000 + centiseconds * 10;
        }

        _timer!.cancel();
        _timer = null;
      }

      // 录音结束后重置时间显示为初始状态
      _updateAudioTime("00:00");
      _notifyStateChanged(); // 录音状态变化，需要更新UI
      _playerController.dispose();
      _playerController = PlayerController();
      await init();
    } catch (e, stackTrace) {
      AppLogger.warning('停止录音失败: $e', stackTrace: stackTrace);
      _isRecording = false;
      _notifyStateChanged();
    }
  }

  Future<void> _preparePlayerController() async {
    try {
      // 检查文件是否存在
      final file = File(_recordingPath);
      if (!await file.exists()) {
        AppLogger.warning('录音文件不存在: $_recordingPath');
        return;
      }

      // 设置波形数据提取监听
      _playerController.onCurrentExtractedWaveformData.listen((data) {
        _notifyStateChanged(); // 波形数据更新时通知UI
      });

      // 为播放准备波形数据
      await _playerController.preparePlayer(
        path: _recordingPath,
        noOfSamples: 100,
        shouldExtractWaveform: true, // 关键：启用波形数据提取
      );
      _notifyStateChanged(); // 播放准备状态变化，需要更新UI
    } catch (e, stackTrace) {
      AppLogger.warning('准备播放器失败: $e', stackTrace: stackTrace);
      print('_preparePlayerController: 异常: $e');
    }
  }

  Future<void> playRecording() async {
    if (!_hasRecorded || _isPlaying) return;

    try {
      // 检查文件是否存在
      final file = File(_recordingPath);
      if (!await file.exists()) {
        AppLogger.warning('录音文件不存在，无法播放');
        return;
      }
      final maxPlayDuration =
          await _playerController.getDuration(DurationType.max);
      // 使用PlayerController播放音频和显示波形
      await _playerController.startPlayer();
      _isPlaying = true;
      int tmpseconds = 0;
      _notifyStateChanged(); // 播放状态变化，需要更新UI
      // 启动计时器更新播放时间，每50毫秒更新一次，让最后一位数字有变化
      _timer = Timer.periodic(const Duration(milliseconds: 50), (timer) async {
        if (!_isPlaying || tmpseconds > maxPlayDuration) {
          timer.cancel();
          _timer = null;
          return;
        }
        tmpseconds = await _playerController.getDuration(DurationType.current);
        // 更新播放时间（只更新时间显示，不重建整个UI）
        // 格式为"秒:毫秒"，如"10:98"表示10秒980毫秒
        final seconds1 = (tmpseconds ~/ 1000).toString().padLeft(2, '0');
        final seconds2 = ((tmpseconds % 1000) ~/ 10).toString().padLeft(2, '0');
        _updateAudioTime("$seconds1:$seconds2");
      });
    } catch (e, stackTrace) {
      AppLogger.warning('播放录音失败: $e', stackTrace: stackTrace);
      _isPlaying = false;
      _notifyStateChanged();
    }
  }

  Future<void> stopPlayback() async {
    if (!_isPlaying) return;

    try {
      await _playerController.pausePlayer();
      _isPlaying = false;
      _notifyStateChanged(); // 播放状态变化，需要更新UI
    } catch (e, stackTrace) {
      AppLogger.warning('停止播放失败: $e', stackTrace: stackTrace);
    }
  }

  Future<void> deleteRecording() async {
    try {
      await stopPlayback();

      final file = File(_recordingPath);
      if (await file.exists()) {
        await file.delete();
      }

      _hasRecorded = false;
      _updateAudioTime("00:00");
      _notifyStateChanged(); // 录音状态变化，需要更新UI
    } catch (e, stackTrace) {
      AppLogger.warning('删除录音失败: $e', stackTrace: stackTrace);
    }
  }

  Future<void> _loadExistingRecordingDuration() async {
    try {
      // 检查文件是否存在
      final file = File(_recordingPath);
      if (!await file.exists()) {
        AppLogger.warning('录音文件不存在: $_recordingPath');
        return;
      }

      // 获取录音文件的时长，但不更新显示时间（保持初始状态00:00）
      final duration = await _playerController.getDuration(DurationType.max);
      if (duration != null) {
        _totalRecordingDuration = duration.toInt();
      }
      // 保持时间显示为初始状态
      _updateAudioTime("00:00");
    } catch (e, stackTrace) {
      AppLogger.warning('加载已存在录音时长失败: $e', stackTrace: stackTrace);
    }
  }

  void dispose() {
    _timer?.cancel();
    audioTimeNotifier.dispose(); // 记得释放ValueNotifier
    _recorderController.dispose();
    _playerController.dispose();
  }
}
