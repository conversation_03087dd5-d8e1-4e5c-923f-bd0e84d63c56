import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:esp_blufi/esp_blufi.dart';

import '../constants/constants.dart';
import '../widgets/device_scan_painters.dart';
import '../utils/app_logger.dart';

class DeviceScanScreen extends StatefulWidget {
  @override
  _DeviceScanScreenState createState() => _DeviceScanScreenState();
}

class _DeviceScanScreenState extends State<DeviceScanScreen>
    with TickerProviderStateMixin {
  // 调用ESP32的Blufi蓝牙配网插件
  final _espBlufiPlugin = EspBlufi();
  // 蓝牙扫描状态
  bool _isScanning = false;
  // 扫描超时时间（秒）
  final int _scanTimeoutDuration = 10;

  // 动画控制器
  late AnimationController _controller;
  Map<String, dynamic> scanResult = {};

  @override
  void initState() {
    super.initState();

    // 执行动画
    _controller = AnimationController(
      vsync: this,
      duration: Duration(seconds: _scanTimeoutDuration),
    )..repeat();

    // 设置蓝牙扫描回调
    _setupBlufiCallback();

    // 页面加载后开始扫描
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startScanning();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _stopScanning();
    super.dispose();
  }

  // 设置蓝牙扫描回调
  void _setupBlufiCallback() {
    _espBlufiPlugin.onMessageReceived(successCallback: (data) {
      if (data == null || !mounted) return;

      Map<String, dynamic> mapData = jsonDecode(data);
      if (mapData.containsKey('key')) {
        String key = mapData['key'];
        if (key == 'ble_scan_result') {
          Map<String, dynamic> peripheral = mapData['value'];
          String address = peripheral['address'];
          String name = peripheral['name'];
          // 检查设备名称是否符合要求
          if (name.contains(AppConfig.bleName)) {
            if (!scanResult.containsKey(address)) {
              setState(() {
                scanResult[address] = name;
              });
            }
          }
        } else if (key == 'stop_scan_ble') {
          setState(() {
            _isScanning = false;
          });
          _pauseAnimation();
        }
      }
    });
  }

  // 暂停动画
  void _pauseAnimation() {
    if (_controller.isAnimating) {
      _controller.stop();
    }
  }

  // 重置动画
  void _resumeAnimation() {
    if (!_controller.isAnimating) {
      _controller.reset();
      _controller.repeat();
    }
  }

  // 停止扫描设备蓝牙
  Future<void> _stopScanning() async {
    if (_isScanning) {
      await _espBlufiPlugin.stopScan();
      if (mounted) {
        setState(() {
          _isScanning = false;
        });
      }
      _pauseAnimation();
    }
  }

  // 开始扫描设备蓝牙
  Future<void> _startScanning() async {
    if (_isScanning) {
      return;
    }
    // 清空之前的结果
    setState(() {
      scanResult.clear();
      _isScanning = true;
    });
    _resumeAnimation();
    try {
      await _espBlufiPlugin.scanDeviceInfo(filterString: AppConfig.bleName);
      // 设置扫描超时
      Future.delayed(Duration(seconds: _scanTimeoutDuration), () {
        _stopScanning();
      });
    } catch (e, s) {
      AppLogger.error("启动蓝牙扫描失败: $e", error: e, stackTrace: s);
      _stopScanning();
    }
  }

  void _connectRoutes(String blueMac) {
    // if (_isScanning) {
    //   _stopScanning();
    // }
    Navigator.of(context).pushNamed(Routes.deviceConnect, arguments: blueMac);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('添加设备'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 雷达图区域
            SizedBox(
              height: 250,
              width: 250,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // 圆圈动画
                  Positioned.fill(
                    child: RepaintBoundary(
                      child: AnimatedBuilder(
                        animation: _controller,
                        builder: (context, child) {
                          return CustomPaint(
                            painter: CirclesPainter(_controller.value),
                          );
                        },
                      ),
                    ),
                  ),
                  // 扫描线动画
                  Positioned.fill(
                    child: RepaintBoundary(
                      child: AnimatedBuilder(
                        animation: _controller,
                        builder: (context, child) {
                          return Transform.rotate(
                            angle: _controller.value * 2 * pi,
                            child: CustomPaint(
                              painter: ScanLinePainter(),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 20),
            // 扫描状态文本
            Text(
              _isScanning ? '正在扫描.....' : '扫描结束',
              style: TextStyle(fontSize: 20),
            ),
            SizedBox(height: 10),
            Text(
              _isScanning
                  ? '正在搜索可用设备...'
                  : (scanResult.isEmpty
                      ? '没有找到可用设备,请确认设备已开机或重新扫描'
                      : '未发现想要的设备,请确认设备已开机或重新扫描'),
              style: TextStyle(fontSize: 15, color: Colors.grey),
            ),

            // 重新扫描按钮
            if (!_isScanning)
              Padding(
                padding: const EdgeInsets.only(top: 10.0),
                child: ElevatedButton(
                  onPressed: _startScanning,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orangeAccent,
                    foregroundColor: Colors.white,
                  ),
                  child: Text('重新扫描'),
                ),
              ),

            SizedBox(height: 20),

            // 设备列表
            Expanded(
              child: scanResult.isEmpty
                  ? Center(
                      child: Text(
                        _isScanning ? '搜索中...' : '暂无设备',
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                  : ListView.builder(
                      itemCount: scanResult.length,
                      itemBuilder: (context, index) {
                        List<MapEntry<String, dynamic>> entries =
                            scanResult.entries.toList();
                        final device = entries[index];
                        return Container(
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(16.0),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.2),
                                blurRadius: 5,
                                offset: Offset(0, 2),
                              ),
                            ],
                          ),
                          margin: EdgeInsets.symmetric(
                              horizontal: 16.0, vertical: 8.0),
                          child: ListTile(
                            leading: Icon(Icons.bluetooth_connected,
                                color: Colors.orangeAccent),
                            title: Text(device.value),
                            subtitle: Text(device.key),
                            trailing: ElevatedButton(
                              onPressed: () => _connectRoutes(device.key),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.orangeAccent,
                                foregroundColor: Colors.white,
                              ),
                              child: Text('连接'),
                            ),
                            onTap: () => _connectRoutes(device.key),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}
