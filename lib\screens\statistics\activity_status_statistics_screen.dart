import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../widgets/time_period_selector.dart';
import '../../widgets/chart_view_selector.dart';
import '../../services/mock_data_service.dart';
import '../../models/extended_health_data.dart';
import '../../constants/activity_status.dart';

/// 活动状态统计界面
class ActivityStatusStatisticsScreen extends StatefulWidget {
  @override
  _ActivityStatusStatisticsScreenState createState() => _ActivityStatusStatisticsScreenState();
}

class _ActivityStatusStatisticsScreenState extends State<ActivityStatusStatisticsScreen> {
  TimePeriod _selectedPeriod = TimePeriod.day;
  ChartViewType _selectedChartType = ChartViewType.bar;
  DateTime _currentDate = DateTime.now();
  List<ExtendedHealthData> _healthData = [];
  String? _selectedActivityStatus;
  bool _isLoading = true;

  /// 多曲线图中各活动状态的可见性控制
  Map<ActivityStatus, bool> _visibleStatuses = {
    for (ActivityStatus status in ActivityStatus.values) status: true,
  };

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<ExtendedHealthData> data;
      switch (_selectedPeriod) {
        case TimePeriod.day:
          data = MockDataService.generateDayData(date: _currentDate);
          break;
        case TimePeriod.week:
          data = MockDataService.generateWeekData(startDate: _currentDate);
          break;
        case TimePeriod.month:
          data = MockDataService.generateMonthData(month: _currentDate);
          break;
      }

      setState(() {
        _healthData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载数据失败: $e')),
      );
    }
  }

  /// 切换活动状态的可见性
  void _toggleStatusVisibility(ActivityStatus status) {
    setState(() {
      _visibleStatuses[status] = !(_visibleStatuses[status] ?? true);
    });
  }

  /// 聚合活动状态数据
  Map<ActivityStatus, List<FlSpot>> _aggregateActivityStatusData() {
    final Map<ActivityStatus, List<FlSpot>> result = {};

    // 初始化所有状态的数据列表
    for (final status in ActivityStatus.values) {
      result[status] = [];
    }

    switch (_selectedPeriod) {
      case TimePeriod.day:
        // 日模式：按15分钟时间段聚合
        _aggregateDayData(result);
        break;
      case TimePeriod.week:
        // 周模式：按天聚合
        _aggregateWeekData(result);
        break;
      case TimePeriod.month:
        // 月模式：按日聚合
        _aggregateMonthData(result);
        break;
    }

    return result;
  }

  /// 聚合日模式数据（每15分钟一个数据点）
  void _aggregateDayData(Map<ActivityStatus, List<FlSpot>> result) {
    // 按15分钟时间段分组统计
    final Map<int, Map<ActivityStatus, int>> timeSlotCounts = {};

    for (int i = 0; i < _healthData.length; i++) {
      final data = _healthData[i];
      final timeSlot = i; // 每个数据点代表一个15分钟时间段

      if (!timeSlotCounts.containsKey(timeSlot)) {
        timeSlotCounts[timeSlot] = {for (final status in ActivityStatus.values) status: 0};
      }

      timeSlotCounts[timeSlot]![data.activityStatus] =
          (timeSlotCounts[timeSlot]![data.activityStatus] ?? 0) + 1;
    }

    // 转换为FlSpot数据
    for (final status in ActivityStatus.values) {
      for (final entry in timeSlotCounts.entries) {
        final x = entry.key.toDouble();
        final y = entry.value[status]?.toDouble() ?? 0.0;
        result[status]!.add(FlSpot(x, y));
      }
    }
  }

  /// 聚合周模式数据（每天一个数据点）
  void _aggregateWeekData(Map<ActivityStatus, List<FlSpot>> result) {
    // 按天分组统计
    final Map<int, Map<ActivityStatus, int>> dayCounts = {};

    for (int i = 0; i < _healthData.length; i++) {
      final data = _healthData[i];
      final dayIndex = i; // 每个数据点代表一天

      if (!dayCounts.containsKey(dayIndex)) {
        dayCounts[dayIndex] = {for (final status in ActivityStatus.values) status: 0};
      }

      dayCounts[dayIndex]![data.activityStatus] =
          (dayCounts[dayIndex]![data.activityStatus] ?? 0) + 1;
    }

    // 转换为FlSpot数据
    for (final status in ActivityStatus.values) {
      for (final entry in dayCounts.entries) {
        final x = entry.key.toDouble();
        final y = entry.value[status]?.toDouble() ?? 0.0;
        result[status]!.add(FlSpot(x, y));
      }
    }
  }

  /// 聚合月模式数据（每日一个数据点）
  void _aggregateMonthData(Map<ActivityStatus, List<FlSpot>> result) {
    // 按日分组统计
    final Map<int, Map<ActivityStatus, int>> dayCounts = {};

    for (int i = 0; i < _healthData.length; i++) {
      final data = _healthData[i];
      final dayIndex = i; // 每个数据点代表一天

      if (!dayCounts.containsKey(dayIndex)) {
        dayCounts[dayIndex] = {for (final status in ActivityStatus.values) status: 0};
      }

      dayCounts[dayIndex]![data.activityStatus] =
          (dayCounts[dayIndex]![data.activityStatus] ?? 0) + 1;
    }

    // 转换为FlSpot数据
    for (final status in ActivityStatus.values) {
      for (final entry in dayCounts.entries) {
        final x = entry.key.toDouble();
        final y = entry.value[status]?.toDouble() ?? 0.0;
        result[status]!.add(FlSpot(x, y));
      }
    }
  }

  /// 构建多曲线图的曲线数据
  List<LineChartBarData> _buildMultiLineChartBars(Map<ActivityStatus, List<FlSpot>> visibleData) {
    final List<LineChartBarData> lineBars = [];

    for (final entry in visibleData.entries) {
      final status = entry.key;
      final spots = entry.value;
      final color = Color(int.parse('0xFF${status.colorHex.substring(1)}'));

      lineBars.add(
        LineChartBarData(
          spots: spots,
          isCurved: true, // 使用光滑曲线
          curveSmoothness: 0.35, // 设置曲线平滑度，值越大越平滑
          color: color,
          barWidth: 1.5, // 线条宽度减小一倍
          dotData: FlDotData(show: false), // 不显示曲线上的小圆点
          belowBarData: BarAreaData(show: false), // 多曲线图不显示填充区域
        ),
      );
    }

    return lineBars;
  }

  /// 构建多曲线图的Tooltip项
  List<LineTooltipItem> _buildMultiLineTooltipItems(List<LineBarSpot> touchedBarSpots) {
    final List<LineTooltipItem> tooltipItems = [];

    if (touchedBarSpots.isNotEmpty) {
      final index = touchedBarSpots.first.x.toInt();
      String timeRange = '';

      if (_selectedPeriod == TimePeriod.day) {
        // 日模式显示时间段
        final hour = index ~/ 4;
        final minute = (index % 4) * 15;
        final nextMinute = minute + 15;
        if (nextMinute == 60) {
          timeRange = '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}~${(hour + 1).toString().padLeft(2, '0')}:00';
        } else {
          timeRange = '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}~${hour.toString().padLeft(2, '0')}:${nextMinute.toString().padLeft(2, '0')}';
        }
      } else if (_selectedPeriod == TimePeriod.month) {
        // 月模式显示具体日期
        final day = index + 1;
        final month = DateTime.now().month; // 使用当前月份
        timeRange = '${month}月${day}日';
      } else {
        // 周模式显示星期
        final weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
        timeRange = weekdays[index % 7];
      }

      // 为每个触摸点创建对应的tooltip项
      final visibleStatuses = ActivityStatus.values.where((status) =>
          _visibleStatuses[status] == true).toList();

      for (int i = 0; i < touchedBarSpots.length; i++) {
        final barSpot = touchedBarSpots[i];

        if (i < visibleStatuses.length) {
          final status = visibleStatuses[i];
          final color = Color(int.parse('0xFF${status.colorHex.substring(1)}'));

          // 第一个tooltip项包含时间范围，其他项只显示状态信息
          final displayText = i == 0
              ? '$timeRange\n${status.displayName}: ${barSpot.y.toInt()}次'
              : '${status.displayName}: ${barSpot.y.toInt()}次';

          tooltipItems.add(
            LineTooltipItem(
              displayText,
              TextStyle(
                color: i == 0 ? Colors.white : color,
                fontWeight: i == 0 ? FontWeight.bold : FontWeight.w600,
                fontSize: i == 0 ? 12 : 11,
              ),
            ),
          );
        } else {
          // 如果没有对应的状态，添加空的tooltip项以保持数量一致
          tooltipItems.add(
            LineTooltipItem(
              '',
              const TextStyle(color: Colors.transparent),
            ),
          );
        }
      }
    }

    return tooltipItems;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('活动状态统计'),
        backgroundColor: Colors.green.shade50,
      ),
      body: Column(
        children: [
          // 时间段选择器
          TimePeriodSelectorWithNavigation(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
              });
              _loadData();
            },
            currentDate: _currentDate,
            onDateChanged: (date) {
              setState(() {
                _currentDate = date;
              });
              _loadData();
            },
          ),
          
          // 图表类型和活动状态选择器
          CombinedChartSelector(
            availableChartTypes: ChartTypePresets.activityStatusStatistics,
            selectedChartType: _selectedChartType,
            onChartTypeChanged: (type) {
              setState(() {
                _selectedChartType = type;
              });
            },
            availableStatuses: ActivityStatus.values.map((s) => s.displayName).toList(),
            selectedStatus: _selectedActivityStatus,
            onStatusChanged: (status) {
              setState(() {
                _selectedActivityStatus = status;
              });
            },
          ),
          
          // 图表区域
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator())
                : Padding(
                    padding: EdgeInsets.all(16),
                    child: Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _getChartTitle(),
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            SizedBox(height: 16),
                            Expanded(
                              child: _buildChart(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
          ),
          
          // 多曲线图图例
          if (!_isLoading && _selectedChartType == ChartViewType.multiLine)
            _buildActivityStatusLegend(),

          // 统计信息
          if (!_isLoading) _buildStatisticsInfo(),
        ],
      ),
    );
  }

  /// 构建图表
  Widget _buildChart() {
    if (_healthData.isEmpty) {
      return Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    switch (_selectedChartType) {
      case ChartViewType.bar:
        return _buildBarChart();
      case ChartViewType.multiLine:
        return _buildMultiLineChart();
      default:
        return _buildBarChart();
    }
  }

  /// 获取图表标题
  String _getChartTitle() {
    switch (_selectedChartType) {
      case ChartViewType.multiLine:
        return '活动状态趋势对比';
      case ChartViewType.bar:
      default:
        return '活动状态分布';
    }
  }

  /// 构建多曲线图（所有活动状态的趋势对比）
  Widget _buildMultiLineChart() {
    final aggregatedData = _aggregateActivityStatusData();

    // 过滤出可见的状态数据
    final visibleData = <ActivityStatus, List<FlSpot>>{};
    for (final entry in aggregatedData.entries) {
      if (_visibleStatuses[entry.key] == true && entry.value.isNotEmpty) {
        visibleData[entry.key] = entry.value;
      }
    }

    if (visibleData.isEmpty) {
      return Center(
        child: Text(
          '暂无可显示的数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    return AspectRatio(
      aspectRatio: 2.0, // 设置宽高比为2:1
      child: LineChart(
        LineChartData(
          minY: 0, // 设置Y轴最小值为0
          maxY: _getMaxYValue(visibleData), // 设置Y轴最大值
          gridData: FlGridData(
            show: true,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: Colors.grey.shade300,
                strokeWidth: 1,
                dashArray: [5, 5], // 设置虚线样式
              );
            },
          ),
          titlesData: FlTitlesData(
            leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
                interval: _getBottomTitleInterval(), // 根据不同模式设置不同间隔
                getTitlesWidget: (value, meta) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0), // 向下推进横坐标值
                    child: Text(
                      _getBottomTitle(value.toInt()),
                      style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                    ),
                  );
                },
              ),
            ),
            rightTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40, // 减少右侧预留空间，让数值更靠近右边缘
                interval: _getYAxisInterval(visibleData), // 设置Y轴间隔
                getTitlesWidget: (value, meta) {
                  final interval = _getYAxisInterval(visibleData).toInt();
                  final intValue = value.toInt();

                  // 只显示符合间隔的非负整数值
                  if (intValue >= 0 && interval > 0 && intValue % interval == 0) {
                    return Padding(
                      padding: const EdgeInsets.only(left: 8.0), // 向右推进数值
                      child: Text(
                        intValue.toString(),
                        style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                        textAlign: TextAlign.right,
                      ),
                    );
                  }
                  return SizedBox.shrink(); // 不显示非间隔值
                },
              ),
            ), // 显示右侧数值标题
            topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
          ),
          borderData: FlBorderData(show: false), // 去掉坐标轴外框
          lineTouchData: LineTouchData(
            enabled: true,
            handleBuiltInTouches: true, // 确保内置触摸处理正常
            touchTooltipData: LineTouchTooltipData(
              getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                return _buildMultiLineTooltipItems(touchedBarSpots);
              },
            ),
            touchCallback: (FlTouchEvent event, LineTouchResponse? touchResponse) {
              // 不执行任何状态更新，只处理tooltip显示
              // 这样可以避免触摸时意外的状态改变
            },
          ),
          lineBarsData: _buildMultiLineChartBars(visibleData),
        ),
      ),
    );
  }



  /// 构建柱状图（活动状态分布）
  Widget _buildBarChart() {
    // 统计各活动状态的出现次数
    final statusCounts = <ActivityStatus, int>{};
    for (final status in ActivityStatus.values) {
      statusCounts[status] = _healthData.where((data) => data.activityStatus == status).length;
    }

    final barGroups = statusCounts.entries.map((entry) {
      final index = ActivityStatus.values.indexOf(entry.key);
      final color = Color(int.parse('0xFF${entry.key.colorHex.substring(1)}'));
      
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: entry.value.toDouble(),
            color: color,
            width: 20,
            borderRadius: BorderRadius.circular(4),
          ),
        ],
      );
    }).toList();

    return AspectRatio(
      aspectRatio: 2.0, // 设置宽高比为2:1
      child: BarChart(
        BarChartData(
          gridData: FlGridData(
            show: true,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: Colors.grey.shade300,
                strokeWidth: 1,
                dashArray: [5, 5], // 设置虚线样式
              );
            },
          ),
          titlesData: FlTitlesData(
            leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
            rightTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40, // 减少右侧预留空间，让数值更靠近右边缘
                getTitlesWidget: (value, meta) {
                  return Padding(
                    padding: const EdgeInsets.only(left: 8.0), // 向右推进数值
                    child: Text(
                      value.toInt().toString(),
                      style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                      textAlign: TextAlign.right,
                    ),
                  );
                },
              ),
            ), // 显示右侧数值标题
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
              getTitlesWidget: (value, meta) {
                if (value.toInt() >= 0 && value.toInt() < ActivityStatus.values.length) {
                  return Padding(
                    padding: const EdgeInsets.only(top: 8.0), // 向下推进横坐标值
                    child: Text(
                      ActivityStatus.values[value.toInt()].displayName,
                      style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                      textAlign: TextAlign.center,
                    ),
                  );
                }
                return Text('');
              },
            ),
          ),
          topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: false), // 去掉坐标轴外框
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final status = ActivityStatus.values[groupIndex];
              return BarTooltipItem(
                '${status.displayName}\n${rod.toY.toInt()}次',
                const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              );
            },
          ),
        ),
        barGroups: barGroups,
      ),
    ),
    );
  }

  /// 获取底部标题间隔
  double _getBottomTitleInterval() {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return 12.0; // 日模式：3小时间隔(12个15分钟)
      case TimePeriod.week:
        return 1.0; // 周模式：每天显示
      case TimePeriod.month:
        return 5.0; // 月模式：每5天显示一个标签
    }
  }

  /// 获取底部标题
  String _getBottomTitle(int index) {
    if (index >= _healthData.length) return '';
    
    final data = _healthData[index];
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return '${data.datetime.hour}:${data.datetime.minute.toString().padLeft(2, '0')}';
      case TimePeriod.week:
        return '${data.datetime.month}/${data.datetime.day}';
      case TimePeriod.month:
        return '${data.datetime.day}日';
    }
  }

  /// 构建统计信息
  Widget _buildStatisticsInfo() {
    // 统计各活动状态的出现次数和时长
    final statusCounts = <ActivityStatus, int>{};
    for (final status in ActivityStatus.values) {
      statusCounts[status] = _healthData.where((data) => data.activityStatus == status).length;
    }

    final mostActiveStatus = statusCounts.entries.reduce((a, b) => a.value > b.value ? a : b);
    final totalRecords = _healthData.length;

    return Container(
      padding: EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('最常见状态', mostActiveStatus.key.displayName, 
                      Color(int.parse('0xFF${mostActiveStatus.key.colorHex.substring(1)}'))),
                  _buildStatItem('出现次数', '${mostActiveStatus.value}', Colors.blue),
                  _buildStatItem('占比', '${(mostActiveStatus.value / totalRecords * 100).toStringAsFixed(1)}%', Colors.green),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// 构建活动状态图例
  Widget _buildActivityStatusLegend() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '活动状态图例',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade700,
                ),
              ),
              SizedBox(height: 12),
              Wrap(
                spacing: 12,
                runSpacing: 8,
                children: ActivityStatus.values.map((status) {
                  final isVisible = _visibleStatuses[status] ?? true;
                  final color = Color(int.parse('0xFF${status.colorHex.substring(1)}'));

                  return GestureDetector(
                    onTap: () => _toggleStatusVisibility(status),
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: isVisible ? color.withOpacity(0.1) : Colors.transparent,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isVisible ? color : Colors.grey.shade300,
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            width: 12,
                            height: 12,
                            decoration: BoxDecoration(
                              color: isVisible ? color : Colors.grey.shade300,
                              shape: BoxShape.circle,
                            ),
                          ),
                          SizedBox(width: 6),
                          Text(
                            status.displayName,
                            style: TextStyle(
                              fontSize: 12,
                              color: isVisible ? color : Colors.grey.shade500,
                              fontWeight: isVisible ? FontWeight.w500 : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取Y轴最大值
  double _getMaxYValue(Map<ActivityStatus, List<FlSpot>> visibleData) {
    if (visibleData.isEmpty) return 10.0;

    // 找到所有可见数据中的最大值
    double maxValue = 0;
    for (final spots in visibleData.values) {
      for (final spot in spots) {
        if (spot.y > maxValue) {
          maxValue = spot.y;
        }
      }
    }

    // 在最大值基础上增加一些余量，确保图表美观
    if (maxValue == 0) return 10.0;
    return (maxValue * 1.2).ceilToDouble();
  }

  /// 获取Y轴间隔
  double _getYAxisInterval(Map<ActivityStatus, List<FlSpot>> visibleData) {
    if (visibleData.isEmpty) return 1.0;

    final maxValue = _getMaxYValue(visibleData);

    // 根据最大值确定合适的间隔
    if (maxValue <= 5) {
      return 1.0;
    } else if (maxValue <= 10) {
      return 2.0;
    } else if (maxValue <= 20) {
      return 5.0;
    } else if (maxValue <= 50) {
      return 10.0;
    } else if (maxValue <= 100) {
      return 20.0;
    } else {
      return (maxValue / 5).ceilToDouble();
    }
  }
}
