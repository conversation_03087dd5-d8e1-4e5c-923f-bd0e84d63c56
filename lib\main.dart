import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:pet_care/screens/device_scan_screen.dart';
import 'package:provider/provider.dart';
import 'package:flutter_localizations/flutter_localizations.dart';

import 'providers/auth_provider.dart';
import 'providers/device_provider.dart';
import 'providers/health_provider.dart';

import 'services/api_service.dart';
import 'services/auth_service.dart';
import 'services/ble_service.dart';
import 'services/health_service.dart'; // 导入 HealthService
import 'services/device_service.dart';
import 'services/anti_lost_service.dart';

import 'screens/home_screen.dart';
import 'screens/login_screen.dart';
import 'screens/profile_screen.dart';
import 'screens/health_screen.dart';
import 'screens/qr_screen.dart';
import 'screens/device_connect_screen.dart';
import 'screens/device_control_screen.dart';
import 'screens/device_registration_screen.dart';
import 'screens/anti_lost_monitor_screen.dart';
import 'screens/utrasonic_config.dart';
import 'screens/audio_record_screen.dart';
import 'screens/device_manage_screen.dart';
import 'screens/pet_info_input_screen.dart';
import 'screens/pet_detail_screen.dart';
import 'models/pet.dart';

import 'utils/l10n.dart'; // 引入国际化支持
import 'utils/toast_utils.dart'; // 导入toast工具类
import 'utils/app_logger.dart'; // 导入 AppLogger
import 'utils/permissions.dart'; // 导入 PermissionsTool

import '../constants/constants.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  // 设置同步异常处理
  // FlutterError.onError = (FlutterErrorDetails details) {
  //   AppLogger.error(
  //     details.exceptionAsString(),
  //     error: details.exception,
  //     stackTrace: details.stack ?? StackTrace.current,
  //   );
  //   // 显示错误提示
  //   ToastUtils.showError(details.exceptionAsString());
  // };

  // 设置异步异常处理
  PlatformDispatcher.instance.onError = (error, stack) {
    AppLogger.fatal(
      "Unhandled async error caught",
      moduleTag: "PlatformDispatcher", // Tag for this handler
      error: error,
      stackTrace: stack,
    );
    ToastUtils.showError(error.toString());
    return true;
  };

  runApp(
    MultiProvider(
      providers: [
        // 服务提供者
        Provider(
          create: (context) => ApiService(),
        ),
        Provider(
          create: (context) =>
              AuthService(Provider.of<ApiService>(context, listen: false)),
        ),
        Provider(
          create: (context) => DeviceApiService(
            Provider.of<ApiService>(context, listen: false),
          ),
        ),
        Provider(
          create: (context) => DeviceControlService(
            Provider.of<ApiService>(context, listen: false),
          ),
        ),
        Provider(
          create: (_) => BleService(),
        ),
        Provider(
          create: (context) => HealthService(
            Provider.of(context, listen: false),
          ),
        ),
        Provider(
          create: (context) => AntiLostService(
            Provider.of<BleService>(context, listen: false),
          ),
        ),
        // 状态管理提供者
        ChangeNotifierProvider(
          create: (context) => AuthProvider(
            Provider.of<AuthService>(context, listen: false),
          ),
        ),
        ChangeNotifierProvider(
          create: (context) => DeviceProvider(
            Provider.of<DeviceApiService>(context, listen: false),
            Provider.of<DeviceControlService>(context, listen: false),
          ),
        ),
        ChangeNotifierProvider(
          create: (context) => HealthProvider(
            Provider.of(context, listen: false),
          ),
        ),
      ],
      child: MyApp(),
    ),
  );
}

class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final PermissionsTool _permissionManager = PermissionsTool();
  bool _isLoading = true; // 状态变量，用于跟踪初始化是否完成

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  //构建应用前需要执行初始化，主要是请求权限和检查JWT实现免登录
  Future<void> _initializeApp() async {
    try {
      // 1. 请求权限
      await _requestPermissions();
      // 2. 初始化 Providers
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      await authProvider.init();
    } catch (e, s) {
      // 处理初始化过程中可能发生的错误
      AppLogger.error("应用初始化失败", error: e, stackTrace: s);
    } finally {
      // 3. 初始化完成，更新状态以重建UI
      if (mounted) {
        // 检查 widget 是否仍在 widget 树中
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future _requestPermissions() async {
    await _permissionManager.requestStoragePermission();
    await _permissionManager.requestLocationPermission();
    await _permissionManager.requestBluetoothPermission();
    await _permissionManager.requestNotificationPermission();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      // 如果正在加载初始化，显示启动画面
      return MaterialApp(
        home: Scaffold(
          body: Center(
            child: FlutterLogo(size: 100), // 使用 Flutter Logo 作为启动画面
          ),
        ),
      );
    }

    // 初始化完成，构建主应用
    // 此时 authProvider.init() 已经执行完毕，isLogin 状态是正确的
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    return MaterialApp(
      navigatorKey: navigatorKey, // 使用ToastUtils的全局NavigatorKey
      title: 'Pet Care App',
      theme: ThemeData(
        // scaffoldBackgroundColor: Colors.grey[200],
        // primarySwatch: Colors.orange,
        colorScheme: ColorScheme.fromSwatch(
          primarySwatch: Colors.orange, // 主色
          backgroundColor: Colors.grey[300], // 背景色
        ),
        inputDecorationTheme: InputDecorationTheme(
          border: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.orange), // 边框色
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.orange), // 聚焦时的边框色
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(color: Colors.orange), // 启用时的边框色（通常与未聚焦时相同）
          ),
          // 注意：filled 和 fillColor 是在 InputDecoration 中设置的，而不是在 InputDecorationTheme 中。
          // 但你可以在这里定义其他与装饰相关的样式。
        ),
      ),
      // 国际化支持
      supportedLocales: L10n.all,
      localizationsDelegates: [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        // 如果有自定义的本地化代理，请在此添加
      ],
      localeResolutionCallback: (locale, supportedLocales) {
        // 检查当前语言是否被支持
        for (var supportedLocale in supportedLocales) {
          if (supportedLocale.languageCode == locale?.languageCode) {
            return supportedLocale;
          }
        }
        // 如果当前语言不支持，则使用第一个支持的语言
        return supportedLocales.first;
      },
      // 路由配置
      routes: {
        Routes.home: (context) => HomeScreen(),
        Routes.login: (context) => LoginScreen(),
        Routes.profile: (context) => ProfileScreen(),
        Routes.health: (context) => HealthScreen(),
        Routes.ultrasonicConfig: (context) => UltrasonicConfigScreen(),
        Routes.audioRecord: (context) => AudioRecordScreen(),
        Routes.qrCode: (context) => QRCodeScreenProvider(),
        Routes.deviceConnect: (context) => DeviceConnectScreen(),
        Routes.deviceScan: (context) => DeviceScanScreen(),
        Routes.deviceControl: (context) => DeviceControlScreen(),
        Routes.deviceRegistration: (context) => DeviceRegistrationScreen(),
        Routes.deviceManage: (context) => DeviceManageScreen(),
      },
      onGenerateRoute: (settings) {
        switch (settings.name) {
          case Routes.petInfoInput:
            final Pet? existingPet = settings.arguments as Pet?;
            return MaterialPageRoute(
              builder: (context) => PetInfoInputScreen(existingPet: existingPet),
            );
          case Routes.petDetail:
            final Pet pet = settings.arguments as Pet;
            return MaterialPageRoute(
              builder: (context) => PetDetailScreen(pet: pet),
            );
          default:
            return null;
        }
      },
      initialRoute: authProvider.isLogin ? Routes.home : Routes.login,
      builder: (context, child) {
        // 确保防丢监控器在应用的所有路由上都可用
        return AntiLostMonitorWidget(child: child ?? Container());
      },
    );
  }
}
