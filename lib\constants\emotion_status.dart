/// 情绪状态枚举
/// 定义宠物的各种情绪状态类型
enum EmotionStatus {
  /// 焦虑/疼痛
  anxiousPain('焦虑/疼痛'),
  
  /// 紧张/不安
  nervousUnrest('紧张/不安'),
  
  /// 疲劳/倦怠
  fatigueTired('疲劳/倦怠'),
  
  /// 无聊/抑郁
  boredDepressed('无聊/抑郁'),
  
  /// 平稳
  stable('平稳'),
  
  /// 兴奋/高活力
  excitedHighEnergy('兴奋/高活力'),
  
  /// 过度兴奋
  overExcited('过度兴奋');

  const EmotionStatus(this.displayName);

  /// 显示名称
  final String displayName;

  /// 从字符串获取情绪状态
  static EmotionStatus fromString(String value) {
    for (EmotionStatus status in EmotionStatus.values) {
      if (status.displayName == value || status.name == value) {
        return status;
      }
    }
    return EmotionStatus.stable; // 默认值
  }

  /// 获取所有情绪状态的显示名称列表
  static List<String> get allDisplayNames {
    return EmotionStatus.values.map((status) => status.displayName).toList();
  }

  /// 获取情绪状态对应的颜色
  String get colorHex {
    switch (this) {
      case EmotionStatus.anxiousPain:
        return '#F44336'; // 红色
      case EmotionStatus.nervousUnrest:
        return '#FF5722'; // 深橙色
      case EmotionStatus.fatigueTired:
        return '#795548'; // 棕色
      case EmotionStatus.boredDepressed:
        return '#607D8B'; // 蓝灰色
      case EmotionStatus.stable:
        return '#4CAF50'; // 绿色
      case EmotionStatus.excitedHighEnergy:
        return '#FF9800'; // 橙色
      case EmotionStatus.overExcited:
        return '#E91E63'; // 粉红色
    }
  }

  /// 获取情绪状态的情绪等级（1-7，数值越高情绪越积极）
  int get emotionLevel {
    switch (this) {
      case EmotionStatus.anxiousPain:
        return 1;
      case EmotionStatus.nervousUnrest:
        return 2;
      case EmotionStatus.fatigueTired:
        return 3;
      case EmotionStatus.boredDepressed:
        return 4;
      case EmotionStatus.stable:
        return 5;
      case EmotionStatus.excitedHighEnergy:
        return 6;
      case EmotionStatus.overExcited:
        return 7;
    }
  }

  /// 判断是否为负面情绪
  bool get isNegative {
    return emotionLevel < 5;
  }

  /// 判断是否为正面情绪
  bool get isPositive {
    return emotionLevel > 5;
  }

  /// 判断是否为中性情绪
  bool get isNeutral {
    return emotionLevel == 5;
  }
}
