import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/device_provider.dart';
import '../services/device_service.dart';
import '../widgets/ultrasonic_card.dart';
import '../models/ultrasonic_config.dart';
import '../utils/app_logger.dart';

class UltrasonicConfigScreen extends StatefulWidget {
  @override
  _UltrasonicConfigScreenState createState() => _UltrasonicConfigScreenState();
}

class _UltrasonicConfigScreenState extends State<UltrasonicConfigScreen> {
  List<UltrasonicConfig> _configs = [];
  List<UltrasonicConfig> _originalConfigs = [];
  bool _hasChanges = false;
  bool _isLoading = true; // 初始状态为加载中
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadDefaultConfigs();
  }

  // 加载默认配置
  void _loadDefaultConfigs() async {
    setState(() {
      _isLoading = true;
    });

    try {
      _configs = await UltrasonicConfig.getConfig();

      if (_configs.isEmpty) {
        _generateDefaultConfigs();
      }
      _backupConfigs();
    } catch (e, stackTrace) {
      AppLogger.error('加载配置失败: $e', error: e, stackTrace: stackTrace);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  //生成默认配置
  final defaultFrequency = [8.5, 20, 25, 35, 45, 55, 65, 75, 85, 95];
  final defaultDuration = [1.2, 4.3, 4.3, 4.3, 4.3, 4.3, 4.3, 4.3, 4.3, 4.3];
  final defaultEnable = [
    true,
    true,
    true,
    true,
    true,
    true,
    true,
    true,
    true,
    true
  ];
  void _generateDefaultConfigs() {
    if (_configs.isNotEmpty) {
      _configs.clear();
    }
    for (int i = 0; i < 10; i++) {
      _configs.add(UltrasonicConfig(
        index: i,
        frequency: defaultFrequency[i].toDouble(),
        duration: defaultDuration[i].toDouble(),
        isEnabled: defaultEnable[i],
      ));
    }
  }

  // 计算指定索引配置的频率范围
  // 返回 [minFreq, maxFreq] 确保 minFreq < maxFreq
  List<double> _calculateFrequencyRange(int currentIndex) {
    // 检查索引有效性
    if (currentIndex < 0 || currentIndex >= _configs.length) {
      return [1.0, 100.0];
    }

    // 获取所有启用配置的索引和频率
    List<int> enabledIndices = [];
    for (int i = 0; i < _configs.length; i++) {
      if (_configs[i].isEnabled) {
        enabledIndices.add(i);
      }
    }

    // 如果当前配置未启用，返回基础范围
    if (!_configs[currentIndex].isEnabled) {
      return [1.0, 100.0];
    }

    // 找到当前配置在启用配置中的位置
    int currentPositionInEnabled = enabledIndices.indexOf(currentIndex);
    if (currentPositionInEnabled == -1) {
      return [1.0, 100.0];
    }

    // 计算基础最小值和最大值
    double baseMinFreq = 1.0;
    double baseMaxFreq = 100.0;

    // 前一个启用配置的限制
    if (currentPositionInEnabled > 0) {
      int prevEnabledIndex = enabledIndices[currentPositionInEnabled - 1];
      baseMinFreq = _configs[prevEnabledIndex].frequency + 1;
    }

    // 后一个启用配置的限制
    if (currentPositionInEnabled < enabledIndices.length - 1) {
      int nextEnabledIndex = enabledIndices[currentPositionInEnabled + 1];
      baseMaxFreq = _configs[nextEnabledIndex].frequency - 1;
    }

    // 智能范围分配：考虑后续启用配置的需求
    double minFreq = baseMinFreq;
    double maxFreq = baseMaxFreq;

    // 计算后续还有多少个启用配置需要空间
    int remainingEnabledCount =
        enabledIndices.length - currentPositionInEnabled - 1;

    if (remainingEnabledCount > 0) {
      // 为后续配置预留空间，每个配置至少需要1kHz
      double maxAllowedFreq = 100.0 - remainingEnabledCount;
      if (maxFreq > maxAllowedFreq) {
        maxFreq = maxAllowedFreq;
      }
    }

    // 确保范围有效
    if (minFreq < 1.0) minFreq = 1.0;
    if (maxFreq > 100.0) maxFreq = 100.0;
    if (minFreq > 100.0) minFreq = 100.0;
    if (maxFreq < 1.0) maxFreq = 1.0;

    // 确保 minFreq < maxFreq
    if (maxFreq <= minFreq) {
      // 重新分配整个启用配置的频率空间
      _redistributeFrequencySpace(enabledIndices, currentPositionInEnabled);

      // 重新计算当前配置的范围
      if (currentPositionInEnabled > 0) {
        int prevEnabledIndex = enabledIndices[currentPositionInEnabled - 1];
        minFreq = _configs[prevEnabledIndex].frequency + 1;
      } else {
        minFreq = 1.0;
      }

      if (currentPositionInEnabled < enabledIndices.length - 1) {
        int nextEnabledIndex = enabledIndices[currentPositionInEnabled + 1];
        maxFreq = _configs[nextEnabledIndex].frequency - 1;
      } else {
        maxFreq = 100.0;
      }

      // 最后的安全检查
      if (maxFreq <= minFreq) {
        if (minFreq < 100.0) {
          maxFreq = minFreq + 1;
        } else {
          minFreq = 99.0;
          maxFreq = 100.0;
        }
      }
    }

    return [minFreq, maxFreq];
  }

  // 重新分配启用配置的频率空间
  void _redistributeFrequencySpace(
      List<int> enabledIndices, int excludePosition) {
    if (enabledIndices.length <= 1) return;

    // 计算可用的频率空间 (1-100)
    double totalSpace = 99.0; // 100 - 1
    int configCount = enabledIndices.length;

    // 每个配置分配的平均空间
    double spacePerConfig = totalSpace / configCount;

    // 重新分配频率，但跳过当前正在调整的配置
    for (int i = 0; i < enabledIndices.length; i++) {
      if (i == excludePosition) continue; // 跳过当前配置

      int configIndex = enabledIndices[i];
      double newFreq = 1.0 + (i * spacePerConfig) + (spacePerConfig / 2);

      // 确保新频率在合理范围内
      if (newFreq < 1.0) newFreq = 1.0;
      if (newFreq > 100.0) newFreq = 100.0;

      // 确保频率递增
      if (i > 0 && i != excludePosition) {
        int prevIndex = enabledIndices[i - 1];
        if (prevIndex != enabledIndices[excludePosition] &&
            newFreq <= _configs[prevIndex].frequency) {
          newFreq = _configs[prevIndex].frequency + 1;
        }
      }

      _configs[configIndex].frequency = newFreq;
    }
  }

  // 计算指定索引配置的最小频率
  double _calculateMinFrequency(int currentIndex) {
    return _calculateFrequencyRange(currentIndex)[0];
  }

  // 计算指定索引配置的最大频率
  double _calculateMaxFrequency(int currentIndex) {
    return _calculateFrequencyRange(currentIndex)[1];
  }

  // 备份配置（用于检查是否有更改）
  void _backupConfigs() {
    _originalConfigs = _configs.map((config) => config.copy()).toList();
  }

  // 检查是否有未保存的更改
  void _checkForChanges() {
    if (_configs.length != _originalConfigs.length) {
      setState(() {
        _hasChanges = true;
      });
      return;
    }

    for (int i = 0; i < _configs.length; i++) {
      final current = _configs[i];
      final original = _originalConfigs[i];

      if (current.frequency != original.frequency ||
          current.duration != original.duration ||
          current.isEnabled != original.isEnabled) {
        setState(() {
          _hasChanges = true;
        });
        return;
      }
    }

    setState(() {
      _hasChanges = false;
    });
  }

  // 更新频率
  void _updateFrequency(int index, double value) {
    // 计算当前配置的有效范围
    List<double> range = _calculateFrequencyRange(index);
    double minFreq = range[0];
    double maxFreq = range[1];

    // 确保值在有效范围内
    if (value < minFreq) {
      value = minFreq;
    } else if (value > maxFreq) {
      value = maxFreq;
    }

    setState(() {
      _configs[index].frequency = value;

      // 检查是否需要调整其他配置的频率以保持递增顺序
      _ensureFrequencyOrder();
    });
    _checkForChanges();
  }

  // 确保启用配置的频率保持递增顺序
  void _ensureFrequencyOrder() {
    List<int> enabledIndices = [];
    for (int i = 0; i < _configs.length; i++) {
      if (_configs[i].isEnabled) {
        enabledIndices.add(i);
      }
    }

    // 检查并修正频率顺序
    for (int i = 1; i < enabledIndices.length; i++) {
      int currentIndex = enabledIndices[i];
      int prevIndex = enabledIndices[i - 1];

      if (_configs[currentIndex].frequency <= _configs[prevIndex].frequency) {
        // 如果当前频率不大于前一个，调整当前频率
        double newFreq = _configs[prevIndex].frequency + 1;
        if (newFreq <= 100.0) {
          _configs[currentIndex].frequency = newFreq;
        } else {
          // 如果无法向上调整，则向下调整前面的配置
          _adjustPreviousFrequencies(enabledIndices, i);
        }
      }
    }
  }

  // 向下调整前面配置的频率
  void _adjustPreviousFrequencies(List<int> enabledIndices, int fromPosition) {
    for (int i = fromPosition - 1; i >= 0; i--) {
      int currentIndex = enabledIndices[i];
      int nextIndex = enabledIndices[i + 1];

      double maxAllowedFreq = _configs[nextIndex].frequency - 1;
      if (_configs[currentIndex].frequency >= maxAllowedFreq) {
        _configs[currentIndex].frequency =
            maxAllowedFreq > 1.0 ? maxAllowedFreq : 1.0;
      }
    }
  }

  // 更新持续时间
  void _updateDuration(int index, double value) {
    setState(() {
      _configs[index].duration = value;
    });
    _checkForChanges();
  }

  // 更新启用状态
  void _updateEnabled(int index, bool value) {
    setState(() {
      _configs[index].isEnabled = value;
      if (!value) {
        _configs[index].duration = 0;
      } else {
        // 如果启用配置，确保频率在有效范围内
        List<double> range = _calculateFrequencyRange(index);
        double minFreq = range[0];
        double maxFreq = range[1];

        if (_configs[index].frequency < minFreq) {
          _configs[index].frequency = minFreq;
        } else if (_configs[index].frequency > maxFreq) {
          _configs[index].frequency = maxFreq;
        }
      }
    });
    _checkForChanges();
  }

  // 保存配置
  Future<void> _saveConfigs() async {
    setState(() {
      _isSaving = true;
    });

    try {
      final deviceProvider =
          Provider.of<DeviceProvider>(context, listen: false);
      final deviceService =
          Provider.of<DeviceApiService>(context, listen: false);
      final deviceName = deviceProvider.device?.deviceName;

      if (deviceName == null) {
        // 弹窗提示
        showDialog(
          context: context,
          builder: (ctx) => AlertDialog(
            title: Text('设备不存在'),
            content: Text('请先添加设备'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(ctx).pop(),
                child: Text('确定'),
              ),
            ],
          ),
        );
        setState(() {
          _hasChanges = false;
          _isSaving = false;
        });
        return;
      }

      // 将配置转换为JSON字符串，去除index和isEnabled元素
      // 当isEnabled=false时，duration自动设为0
      List<Map<String, dynamic>> configsWithoutIndex = _configs.map((config) {
        return {
          'freq': (config.frequency * 1000).toInt(),
          'duty': config.isEnabled ? (config.duration * 1000).toInt() : 0,
        };
      }).toList();
      String ultrasonicConfigs = jsonEncode(configsWithoutIndex);
      final result = await deviceService.ultrasonicConfigUpdate(
          deviceName, ultrasonicConfigs);

      if (result) {
        await UltrasonicConfig.saveConfig(_configs);
        _backupConfigs();

        setState(() {
          _hasChanges = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 8),
                Text('配置已保存'),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                SizedBox(width: 8),
                Text('保存失败，请重试'),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.error, color: Colors.white),
              SizedBox(width: 8),
              Text('保存失败: $e'),
            ],
          ),
          backgroundColor: Colors.red,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  // 重置为默认配置
  void _resetToDefault() {
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Text('重置配置'),
        content: Text('确定要重置为默认配置吗？这将清除所有当前设置。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(ctx).pop();
              setState(() {
                _generateDefaultConfigs();
              });
              _checkForChanges();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: Text('重置'),
          ),
        ],
      ),
    );
  }

  // 显示未保存更改提示
  Future<bool> _showUnsavedChangesDialog() async {
    final result = await showDialog<bool>(
      context: context,
      builder: (ctx) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.orange),
            SizedBox(width: 8),
            Text('未保存的更改'),
          ],
        ),
        content: Text('检查到您更改了配置，是否保存修改？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(false),
            child: Text('不保存'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(ctx).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
            ),
            child: Text('保存'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  // 处理返回事件
  Future<bool> _onWillPop() async {
    if (_hasChanges) {
      final shouldSave = await _showUnsavedChangesDialog();
      if (shouldSave) {
        await _saveConfigs();
      } else {
        // 用户选择不保存，清除更改状态并退出
        setState(() {
          _hasChanges = false;
        });
      }
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !_hasChanges,
      onPopInvokedWithResult:
          (bool didPop, Future<bool> Function()? popRoute) async {
        if (didPop) return;

        final shouldPop = await _onWillPop();
        if (shouldPop) {
          // 用户选择保存或不保存，都允许退出
          Navigator.of(context).pop();
        }
        // 如果 shouldPop 为 false，用户选择了取消，不执行任何操作
      },
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: Text(
            '超声波配置',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          actions: [
            if (_hasChanges)
              Container(
                margin: EdgeInsets.only(right: 8),
                child: ElevatedButton.icon(
                  icon: _isSaving
                      ? SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Icon(Icons.save, size: 18),
                  label: Text(_isSaving ? '保存中...' : '保存'),
                  onPressed: _isSaving ? null : _saveConfigs,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                ),
              ),
            PopupMenuButton(
              icon: Icon(Icons.more_vert, color: Colors.white),
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'reset',
                  child: Row(
                    children: [
                      Icon(Icons.refresh, color: Colors.orange),
                      SizedBox(width: 8),
                      Text('重置为默认配置'),
                    ],
                  ),
                ),
              ],
              onSelected: (value) {
                if (value == 'reset') {
                  _resetToDefault();
                }
              },
            ),
          ],
        ),
        body: _isLoading
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).primaryColor,
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      '正在加载配置...',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              )
            : Column(
                children: [
                  // 顶部信息卡片
                  Container(
                    width: double.infinity,
                    margin: EdgeInsets.all(16),
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Theme.of(context).primaryColor.withOpacity(0.1),
                          Theme.of(context).primaryColor.withOpacity(0.05),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Theme.of(context).primaryColor.withOpacity(0.2),
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.settings_input_antenna,
                              color: Theme.of(context).primaryColor,
                              size: 24,
                            ),
                            SizedBox(width: 8),
                            Text(
                              '超声波参数配置',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 8),
                        Text(
                          '调整每个声波的频率和持续时间，频率必须递增排列',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                        if (_hasChanges) ...[
                          SizedBox(height: 8),
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: Colors.orange.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(
                                  color: Colors.orange.withOpacity(0.3)),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(Icons.edit,
                                    size: 16, color: Colors.orange),
                                SizedBox(width: 4),
                                Text(
                                  '有未保存的更改',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.orange[700],
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  // 配置列表
                  Expanded(
                    child: ListView.builder(
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      itemCount: _configs.length,
                      itemBuilder: (context, index) {
                        final config = _configs[index];
                        return Container(
                          margin: EdgeInsets.only(bottom: 12),
                          child: UltrasonicCard(
                            index: config.index,
                            frequency: config.frequency,
                            duration: config.duration,
                            isEnabled: config.isEnabled,
                            minFrequency: _calculateMinFrequency(index),
                            maxFrequency: _calculateMaxFrequency(index),
                            onFrequencyChanged: (value) {
                              _updateFrequency(index, value);
                            },
                            onDurationChanged: (value) {
                              _updateDuration(index, value);
                            },
                            onEnabledChanged: (value) {
                              _updateEnabled(index, value);
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
