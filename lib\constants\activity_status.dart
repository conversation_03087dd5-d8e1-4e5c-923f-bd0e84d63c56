/// 活动状态枚举
/// 定义宠物的各种活动状态类型
enum ActivityStatus {
  /// 静止
  stationary('静止'),
  
  /// 走路
  walking('走路'),
  
  /// 快走
  fastWalking('快走'),
  
  /// 慢跑
  jogging('慢跑'),
  
  /// 奔跑
  running('奔跑'),
  
  /// 跳跃
  jumping('跳跃');

  const ActivityStatus(this.displayName);

  /// 显示名称
  final String displayName;

  /// 从字符串获取活动状态
  static ActivityStatus fromString(String value) {
    for (ActivityStatus status in ActivityStatus.values) {
      if (status.displayName == value || status.name == value) {
        return status;
      }
    }
    return ActivityStatus.stationary; // 默认值
  }

  /// 获取所有活动状态的显示名称列表
  static List<String> get allDisplayNames {
    return ActivityStatus.values.map((status) => status.displayName).toList();
  }

  /// 获取活动状态对应的颜色
  String get colorHex {
    switch (this) {
      case ActivityStatus.stationary:
        return '#2196F3'; // 蓝色 - 更鲜艳的颜色替代灰色
      case ActivityStatus.walking:
        return '#4CAF50'; // 绿色
      case ActivityStatus.fastWalking:
        return '#CDDC39'; // 黄绿色 - 与绿色更容易区分
      case ActivityStatus.jogging:
        return '#FF9800'; // 橙色
      case ActivityStatus.running:
        return '#F44336'; // 红色
      case ActivityStatus.jumping:
        return '#9C27B0'; // 紫色
    }
  }

  /// 获取活动状态的强度等级（1-6）
  int get intensityLevel {
    switch (this) {
      case ActivityStatus.stationary:
        return 1;
      case ActivityStatus.walking:
        return 2;
      case ActivityStatus.fastWalking:
        return 3;
      case ActivityStatus.jogging:
        return 4;
      case ActivityStatus.running:
        return 5;
      case ActivityStatus.jumping:
        return 6;
    }
  }
}
