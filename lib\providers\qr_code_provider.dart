import 'package:flutter/foundation.dart';
import '../models/qr_code.dart';
import '../services/qr_code_service.dart';
import '../utils/app_logger.dart';

class QRCodeProvider extends ChangeNotifier {
  final QRCodeService _qrCodeService;

  List<QRCode> _qrCodesList = [];
  bool _isLoading = false;
  String? _error;

  QRCodeProvider(this._qrCodeService);

  List<QRCode> get qrCodes => _qrCodesList;
  String? get error => _error;
  bool get isLoading => _isLoading;
  bool get hasQRCodes => _qrCodesList.isNotEmpty;

  // 获取用户的所有防丢码
  Future<bool> fetchQRCodes() async {
    _isLoading = true;
    _error = null;
    try {
      _qrCodesList = await QRCode.getQRCode();

      if (_qrCodesList.isEmpty) {
        _qrCodesList = await _qrCodeService.getUserQRCodes();
        await saveQRCode();
      }
      return true;
    } catch (e, stackTrace) {
      _error = e.toString();
      AppLogger.error('fetchQRCodes error: $e',
          error: e, stackTrace: stackTrace);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> saveQRCode() async {
    await QRCode.saveQRCode(_qrCodesList);
    return true;
  }

  // 添加新的防丢码
  Future<bool> addQRCode(String url, String contactPhone, String note) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final newQRCode = await _qrCodeService.addQRCode(url, contactPhone, note);
      _qrCodesList.add(newQRCode);
      await saveQRCode();
      return true;
    } catch (e, stackTrace) {
      _error = e.toString();
      AppLogger.error('addQRCode error: $e', error: e, stackTrace: stackTrace);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 删除防丢码
  Future<bool> deleteQRCode(String url) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final success = await _qrCodeService.deleteQRCode(url);
      if (success) {
        _qrCodesList.removeWhere((qrCode) => qrCode.url == url);
      }
      await saveQRCode();
      return success;
    } catch (e, stackTrace) {
      _error = e.toString();
      AppLogger.error('deleteQRCode error: $e',
          error: e, stackTrace: stackTrace);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  // 更新防丢码
  Future<bool> updateQRCode(
      String url, String contactPhone, String note) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      final updatedQRCode =
          await _qrCodeService.updateQRCode(url, contactPhone, note);
      final index = _qrCodesList.indexWhere((qrCode) => qrCode.url == url);
      if (index != -1) {
        _qrCodesList[index] = updatedQRCode;
      }
      await saveQRCode();
      return true;
    } catch (e, stackTrace) {
      _error = e.toString();
      AppLogger.error('updateQRCode error: $e',
          error: e, stackTrace: stackTrace);
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }
}
