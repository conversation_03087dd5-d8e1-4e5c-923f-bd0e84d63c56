# 爱宠信息管理功能

## 功能概述

本功能为Flutter宠物护理应用提供了完整的爱宠信息管理系统，包括信息录入、展示、编辑和删除等核心功能。

## 主要特性

### 1. 数据模型设计
- **Pet模型** (`lib/models/pet.dart`)
  - 支持昵称、种类、品种、体型、生日、肩高、体重等字段
  - 种类枚举：狗狗(dog)、猫咪(cat)
  - 体型枚举：小型(small)、中型(medium)、大型(large)
  - 自动体型计算：根据肩高和体重自动划分体型
  - 年龄计算：支持天、月、年的详细年龄描述

### 2. 体型划分规则
- **猫咪**：统一定义为小型
- **狗狗**：根据深圳市标准自动划分
  - 小型犬：肩高25-40cm 或 体重4-10kg
  - 中型犬：肩高40-60cm 或 体重10-30kg
  - 大型犬：肩高60cm以上 或 体重30kg以上

### 3. 品种数据
- **狗狗品种**：31种常见品种（包含中华田园犬）
- **猫咪品种**：31种常见品种（包含中华田园猫）
- 支持下拉选择预设品种或自定义输入

### 4. 用户界面

#### 爱宠信息录入界面 (`lib/screens/pet_info_input_screen.dart`)
- **动态头部图片**：使用assets/images中的pet_dog.png和pet_cat.png，根据选择的种类自动切换
- **扁平种类选择器**：去除Card容器，使用圆角边框和彩色图标的平铺选择器
- **弹窗品种选择**：点击触发底部弹窗，列表项带浅灰色分隔线，支持图标和选中状态
- **扁平输入字段**：
  - 昵称：红色爱心图标
  - 生日：粉色蛋糕图标，点击触发日期选择器
  - 肩高：绿色高度图标
  - 体重：橙色体重图标
- **圆角提交按钮**：半圆形边角设计，颜色根据宠物种类动态变化（狗狗蓝色，猫咪粉色）
- **完整的表单验证和友好的错误提示**

#### Profile界面爱宠卡片 (`lib/screens/profile_screen.dart`)
- **无爱宠信息状态**：显示编辑图标和提示文字
- **有爱宠信息状态**：身份证样式布局
  - 左侧：昵称、年龄、血统信息
  - 右侧：爱宠头像图片
  - 体型标签显示

#### 爱宠信息详情界面 (`lib/screens/pet_detail_screen.dart`)
- 美观的头部展示区域
- 基本信息卡片：昵称、种类、品种、体型、生日、年龄
- 体征信息卡片：肩高、体重
- 操作按钮：编辑、删除
- 右上角操作菜单

### 5. 数据管理

#### 表单验证
- 必填字段检查
- 数值范围验证（肩高1-200cm，体重0.1-200kg）
- 生日合理性验证
- 友好的错误提示弹窗

#### 数据存储
- 本地存储：使用SharedPreferences
- 用户数据隔离：确保不同用户的数据独立
- JSON序列化：支持完整的数据序列化和反序列化

#### 数据操作
- 创建：新增爱宠信息
- 读取：获取当前用户的爱宠信息
- 更新：编辑现有爱宠信息
- 删除：删除爱宠信息（带二次确认）

### 6. 路由管理
- 使用命名路由管理页面跳转
- 支持参数传递（编辑模式、详情展示）
- 路由配置：
  - `/pet_info_input`：爱宠信息录入/编辑
  - `/pet_detail`：爱宠信息详情

### 7. 用户体验
- **扁平简约设计**：去除传统输入框边框，采用圆角容器设计
- **彩色图标系统**：不同字段使用不同颜色的图标，提升视觉识别度
- **动态头部图片**：根据选择的宠物种类显示对应的pet_dog.png或pet_cat.png
- **圆角按钮设计**：提交按钮采用半圆形边角，颜色随宠物种类变化
- **弹窗品种选择**：品种选择改为底部弹窗模式，带分隔线的列表展示
- **响应式设计**：适配移动端交互
- **加载状态**：显示加载指示器
- **成功反馈**：操作成功后的友好提示
- **错误处理**：完善的错误处理和用户提示
- **数据验证**：实时表单验证

## 技术实现

### 文件结构
```
lib/
├── models/
│   └── pet.dart                    # Pet数据模型
├── screens/
│   ├── pet_info_input_screen.dart  # 爱宠信息录入界面
│   ├── pet_detail_screen.dart      # 爱宠信息详情界面
│   └── profile_screen.dart         # 更新后的Profile界面
├── constants/
│   ├── pet_breeds.dart            # 品种数据常量
│   └── constants.dart             # 路由常量更新
└── utils/
    └── (现有的工具类)              # 用户数据隔离等

test/
└── pet_model_test.dart            # Pet模型单元测试
```

### 依赖关系
- 兼容现有的用户数据隔离机制
- 使用现有的工具类（AppLogger、ToastUtils等）
- 遵循项目现有的代码风格和架构模式

### 数据隔离
- 在`AppConfig.userDataKeys`中添加了`'petInfo'`
- 确保不同用户的爱宠信息完全隔离
- 支持用户切换时的数据清理

## 使用说明

1. **添加爱宠信息**：在Profile页面点击添加按钮进入录入界面
2. **查看爱宠信息**：在Profile页面点击爱宠卡片进入详情界面
3. **编辑爱宠信息**：在详情界面点击编辑按钮或使用右上角菜单
4. **删除爱宠信息**：在详情界面使用删除功能（需二次确认）

## 测试

运行单元测试：
```bash
flutter test test/pet_model_test.dart
```

测试覆盖：
- Pet模型创建和体型计算
- 年龄计算逻辑
- JSON序列化/反序列化
- copyWith方法
- 年龄描述格式化

## 未来扩展

- [ ] API集成：上传爱宠信息到服务器
- [ ] 多宠物支持：支持用户添加多只宠物
- [ ] 照片上传：支持上传宠物照片
- [ ] 健康记录：关联健康数据
- [ ] 疫苗记录：疫苗接种记录管理
