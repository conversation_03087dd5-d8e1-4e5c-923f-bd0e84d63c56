import 'dart:math';
import 'package:flutter/material.dart';

class BatteryAnimation extends StatefulWidget {
  final int batteryLevel;

  const BatteryAnimation({
    Key? key,
    required this.batteryLevel,
  }) : super(key: key);

  @override
  _BatteryAnimationState createState() => _BatteryAnimationState();
}

class _BatteryAnimationState extends State<BatteryAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: Duration(seconds: 5),
    )..repeat();

    _animation = Tween<double>(begin: 0, end: 2 * pi).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.linear,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 电池电量低于98%才显示波浪动画
    final bool showWave = widget.batteryLevel < 98;
    // 电池电量低于20%液体显示红色，否则显示绿色
    final Color liquidColor =
        widget.batteryLevel < 20 ? Colors.red : Colors.green;

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return CustomPaint(
          painter: BatteryPainter(
            batteryLevel: widget.batteryLevel,
            waveOffset: showWave ? _animation.value : 0,
            liquidColor: liquidColor,
            showWave: showWave,
          ),
          child: Center(
            child: Text(
              '${widget.batteryLevel}%',
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18,
                shadows: [
                  Shadow(
                    color: Colors.black54,
                    blurRadius: 2,
                    offset: Offset(1, 1),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class BatteryPainter extends CustomPainter {
  final int batteryLevel;
  final double waveOffset;
  final Color liquidColor;
  final bool showWave;

  BatteryPainter({
    required this.batteryLevel,
    required this.waveOffset,
    required this.liquidColor,
    required this.showWave,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double centerX = size.width / 2;
    final double centerY = size.height / 2;
    final double radius = min(centerX, centerY);

    // 绘制外部圆形边框
    final Paint borderPaint = Paint()
      ..color = Colors.grey[300]!
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    canvas.drawCircle(Offset(centerX, centerY), radius, borderPaint);

    // 绘制液体背景（圆形）
    final Paint backgroundPaint = Paint()
      ..color = Colors.grey[100]!
      ..style = PaintingStyle.fill;
    canvas.drawCircle(Offset(centerX, centerY), radius - 2, backgroundPaint);

    // 绘制液体
    if (batteryLevel > 0) {
      // 计算液体高度
      final double fillHeight = (radius * 2) * (batteryLevel / 100);
      final double fillTop = centerY + radius - fillHeight;

      // 剪裁区域为圆形
      canvas.save();
      final Path clipPath = Path()
        ..addOval(Rect.fromCircle(
            center: Offset(centerX, centerY), radius: radius - 2));
      canvas.clipPath(clipPath);

      // 绘制液体矩形
      final Paint fillPaint = Paint()
        ..color = liquidColor
        ..style = PaintingStyle.fill;

      if (showWave) {
        // 绘制波浪液体
        final Path wavePath = Path();
        wavePath.moveTo(0, size.height);

        final double waveHeight = 1.5; // 增加波浪高度让效果更明显
        final double waveWidth = 60.0; // 调整波浪宽度

        // 起始点
        wavePath.lineTo(0, fillTop);

        // 绘制波浪 - 添加多层波浪效果让动画更自然
        for (double i = 0; i <= size.width; i += 1) {
          final double dx = i;
          // 主波浪
          final double wave1 =
              sin((i / waveWidth + waveOffset) * 2 * pi) * waveHeight;
          // 次波浪（频率和相位不同）
          final double wave2 =
              sin((i / (waveWidth * 0.7) + waveOffset * 0.8) * 2 * pi) *
                  (waveHeight * 0.2);
          // 第三层次波浪（更高频率，更小幅度，模拟细微波动）
          final double wave3 =
              sin((i / (waveWidth * 0.4) + waveOffset * 1.3) * 2 * pi) *
                  (waveHeight * 0.15);
          final double dy = fillTop + wave1 + wave2 + wave3;
          wavePath.lineTo(dx, dy);
        }

        // 关闭路径
        wavePath.lineTo(size.width, size.height);
        wavePath.close();

        canvas.drawPath(wavePath, fillPaint);
      } else {
        // 绘制平静液体
        canvas.drawRect(
          Rect.fromLTWH(0, fillTop, size.width, size.height - fillTop),
          fillPaint,
        );
      }

      canvas.restore();
    }

    // // 绘制光晕效果
    // final Paint highlightPaint = Paint()
    //   ..color = Colors.white.withOpacity(0.2)
    //   ..style = PaintingStyle.fill;
    // final Path highlightPath = Path()
    //   ..addOval(
    //     Rect.fromCircle(
    //       center: Offset(centerX * 0.7, centerY * 0.7),
    //       radius: radius * 0.3,
    //     ),
    //   );
    // canvas.drawPath(highlightPath, highlightPaint);
  }

  @override
  bool shouldRepaint(covariant BatteryPainter oldDelegate) {
    return oldDelegate.batteryLevel != batteryLevel ||
        oldDelegate.waveOffset != waveOffset ||
        oldDelegate.liquidColor != liquidColor ||
        oldDelegate.showWave != showWave;
  }
}
