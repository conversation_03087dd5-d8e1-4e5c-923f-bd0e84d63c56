/// 宠物品种常量定义
class PetBreeds {
  /// 狗狗品种列表
  static const List<String> dogBreeds = [
    '金毛寻回犬',
    '拉布拉多犬',
    '德国牧羊犬',
    '边境牧羊犬',
    '哈士奇',
    '萨摩耶',
    '阿拉斯加雪橇犬',
    '柴犬',
    '秋田犬',
    '博美犬',
    '泰迪犬',
    '比熊犬',
    '雪纳瑞',
    '吉娃娃',
    '约克夏梗',
    '法国斗牛犬',
    '英国斗牛犬',
    '巴哥犬',
    '柯基犬',
    '杜宾犬',
    '罗威纳犬',
    '大丹犬',
    '圣伯纳犬',
    '纽芬兰犬',
    '藏獒',
    '中华田园犬',
    '松狮犬',
    '西施犬',
    '马尔济斯犬',
    '卡斯罗犬',
    '其他',
  ];

  /// 猫咪品种列表
  static const List<String> catBreeds = [
    '英国短毛猫',
    '美国短毛猫',
    '波斯猫',
    '布偶猫',
    '暹罗猫',
    '缅因猫',
    '俄罗斯蓝猫',
    '苏格兰折耳猫',
    '阿比西尼亚猫',
    '孟加拉猫',
    '挪威森林猫',
    '土耳其安哥拉猫',
    '喜马拉雅猫',
    '加菲猫',
    '无毛猫',
    '德文卷毛猫',
    '康沃尔卷毛猫',
    '东方短毛猫',
    '伯曼猫',
    '西伯利亚猫',
    '中华田园猫',
    '橘猫',
    '狸花猫',
    '三花猫',
    '奶牛猫',
    '银渐层',
    '金渐层',
    '蓝猫',
    '白猫',
    '黑猫',
    '其他',
  ];

  /// 根据宠物种类获取品种列表
  static List<String> getBreedsForSpecies(String species) {
    switch (species) {
      case 'dog':
        return dogBreeds;
      case 'cat':
        return catBreeds;
      default:
        return [];
    }
  }

  /// 检查品种是否为预设品种
  static bool isPresetBreed(String species, String breed) {
    final breeds = getBreedsForSpecies(species);
    return breeds.contains(breed);
  }

  /// 获取自定义品种提示文本
  static String getCustomBreedHint(String species) {
    switch (species) {
      case 'dog':
        return '请输入您的狗狗品种';
      case 'cat':
        return '请输入您的猫咪品种';
      default:
        return '请输入品种';
    }
  }
}
