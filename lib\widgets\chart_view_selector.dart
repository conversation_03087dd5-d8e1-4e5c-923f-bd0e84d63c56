import 'package:flutter/material.dart';
import 'time_period_selector.dart';

/// 图表视图类型枚举
enum ChartViewType {
  line('曲线图'),
  bar('柱状图'),
  scatter('散点图'),
  groupedBar('簇状柱形图'),
  horizontalBar('条形图'),
  multiLine('多曲线图');

  const ChartViewType(this.displayName);
  final String displayName;
}

/// 图表视图选择组件
class ChartViewSelector extends StatelessWidget {
  final List<ChartViewType> availableTypes;
  final ChartViewType selectedType;
  final Function(ChartViewType) onTypeChanged;

  const ChartViewSelector({
    Key? key,
    required this.availableTypes,
    required this.selectedType,
    required this.onTypeChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (availableTypes.length <= 1) {
      return SizedBox.shrink(); // 如果只有一种类型，不显示选择器
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: availableTypes.map((type) {
          final isSelected = type == selectedType;
          return Padding(
            padding: EdgeInsets.symmetric(horizontal: 4),
            child: GestureDetector(
              onTap: () => onTypeChanged(type),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                decoration: BoxDecoration(
                  color: isSelected ? Theme.of(context).primaryColor.withOpacity(0.1) : Colors.transparent,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: isSelected ? Theme.of(context).primaryColor : Colors.grey.shade300,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _getIconForType(type),
                      size: 16,
                      color: isSelected ? Theme.of(context).primaryColor : Colors.grey.shade600,
                    ),
                    SizedBox(width: 4),
                    Text(
                      type.displayName,
                      style: TextStyle(
                        color: isSelected ? Theme.of(context).primaryColor : Colors.grey.shade600,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// 获取图表类型对应的图标
  IconData _getIconForType(ChartViewType type) {
    switch (type) {
      case ChartViewType.line:
        return Icons.show_chart;
      case ChartViewType.bar:
        return Icons.bar_chart;
      case ChartViewType.scatter:
        return Icons.scatter_plot;
      case ChartViewType.groupedBar:
        return Icons.bar_chart;
      case ChartViewType.horizontalBar:
        return Icons.bar_chart;
      case ChartViewType.multiLine:
        return Icons.multiline_chart;
    }
  }
}

/// 活动状态选择组件（用于活动状态曲线图的状态切换）
class ActivityStatusSelector extends StatelessWidget {
  final List<String> availableStatuses;
  final String selectedStatus;
  final Function(String) onStatusChanged;

  const ActivityStatusSelector({
    Key? key,
    required this.availableStatuses,
    required this.selectedStatus,
    required this.onStatusChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '选择活动状态:',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade700,
            ),
          ),
          SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 4,
            children: availableStatuses.map((status) {
              final isSelected = status == selectedStatus;
              return GestureDetector(
                onTap: () => onStatusChanged(status),
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: isSelected ? Theme.of(context).primaryColor : Colors.transparent,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: isSelected ? Theme.of(context).primaryColor : Colors.grey.shade300,
                      width: 1,
                    ),
                  ),
                  child: Text(
                    status,
                    style: TextStyle(
                      color: isSelected ? Colors.white : Colors.grey.shade600,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      fontSize: 12,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}

/// 组合图表视图选择器（包含图表类型和活动状态选择）
class CombinedChartSelector extends StatelessWidget {
  final List<ChartViewType> availableChartTypes;
  final ChartViewType selectedChartType;
  final Function(ChartViewType) onChartTypeChanged;
  
  final List<String>? availableStatuses;
  final String? selectedStatus;
  final Function(String)? onStatusChanged;

  const CombinedChartSelector({
    Key? key,
    required this.availableChartTypes,
    required this.selectedChartType,
    required this.onChartTypeChanged,
    this.availableStatuses,
    this.selectedStatus,
    this.onStatusChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 图表类型选择器
        ChartViewSelector(
          availableTypes: availableChartTypes,
          selectedType: selectedChartType,
          onTypeChanged: onChartTypeChanged,
        ),
        
        // 活动状态选择器（仅在需要时显示）
        if (availableStatuses != null &&
            selectedStatus != null &&
            onStatusChanged != null &&
            selectedChartType == ChartViewType.line)
          ActivityStatusSelector(
            availableStatuses: availableStatuses!,
            selectedStatus: selectedStatus!,
            onStatusChanged: onStatusChanged!,
          ),
      ],
    );
  }
}

/// 预定义的图表类型组合
class ChartTypePresets {
  /// 步数统计图表类型
  static const List<ChartViewType> stepStatistics = [
    ChartViewType.line,
    ChartViewType.bar,
  ];

  /// 卡路里统计图表类型
  static const List<ChartViewType> calorieStatistics = [
    ChartViewType.line,
    ChartViewType.bar,
  ];

  /// 体温统计图表类型
  static const List<ChartViewType> temperatureStatistics = [
    ChartViewType.line,
  ];

  /// 活动状态统计图表类型
  static const List<ChartViewType> activityStatusStatistics = [
    ChartViewType.bar,
    ChartViewType.multiLine,
  ];

  /// 情绪状态统计图表类型（根据时间模式动态返回）
  static List<ChartViewType> getEmotionStatusStatistics(TimePeriod period) {
    switch (period) {
      case TimePeriod.day:
        return [ChartViewType.line]; // 日模式只显示曲线图
      case TimePeriod.week:
        return [ChartViewType.groupedBar]; // 周模式只显示簇状柱状图
      case TimePeriod.month:
        return [ChartViewType.multiLine]; // 月模式只显示多曲线图
    }
  }

  /// 情绪状态统计图表类型（保持向后兼容）
  static const List<ChartViewType> emotionStatusStatistics = [
    ChartViewType.line,
    ChartViewType.groupedBar,
    ChartViewType.multiLine,
  ];

  /// 健康监测统计图表类型（根据时间模式动态返回）
  static List<ChartViewType> getHealthDetectionStatistics(TimePeriod period) {
    switch (period) {
      case TimePeriod.day:
        return [ChartViewType.scatter]; // 日模式只显示散点图
      case TimePeriod.week:
        return [ChartViewType.horizontalBar]; // 周模式只显示水平条形图
      case TimePeriod.month:
        return [ChartViewType.horizontalBar]; // 月模式只显示水平条形图
    }
  }

  /// 健康监测统计图表类型（保持向后兼容）
  static const List<ChartViewType> healthDetectionStatistics = [
    ChartViewType.scatter,
    ChartViewType.horizontalBar,
  ];
}
