import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'dart:convert';

class User {
  final String phone;
  final String accessToken;
  final String refreshToken;
  final bool isLogin;

  User(
      {required this.phone,
      required this.accessToken,
      required this.refreshToken,
      required this.isLogin});

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      phone: json['phone'],
      accessToken: json['accessToken'],
      refreshToken: json['refreshToken'],
      isLogin: json['isLogin'],
    );
  }

  Map<String, dynamic> toJson() => {
        'phone': phone,
        'accessToken': accessToken,
        'refreshToken': refreshToken,
        'isLogin': isLogin,
      };

  static Future<User?> getUser() async {
    final storage = FlutterSecureStorage();
    final userJson = await storage.read(key: 'user');
    if (userJson != null) {
      return User.fromJson(jsonDecode(userJson));
    }
    return null;
  }

  static Future<void> saveUser(User user) async {
    final storage = FlutterSecureStorage();
    await storage.write(key: 'user', value: jsonEncode(user.toJson()));
  }

  static Future<void> clearUser() async {
    final storage = FlutterSecureStorage();
    await storage.delete(key: 'user');
  }
}
