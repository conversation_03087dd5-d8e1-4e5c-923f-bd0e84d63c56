import 'package:flutter/material.dart';
import '../models/health_data.dart';
import '../services/health_service.dart';
import '../utils/app_logger.dart';

// HealthProvider 负责管理宠物的健康数据，包括活动量、体温等监测数据的获取和更新。
class HealthProvider extends ChangeNotifier {
  final HealthService _healthService;
  HealthData? _healthData;

  HealthProvider(this._healthService);

  HealthData? get healthData => _healthData;

  // 初始化健康数据
  Future<void> initHealthData() async {
    _healthData = await HealthData.getHealthData();
    notifyListeners();
  }

  // 获取健康数据
  Future<void> fetchHealthData(String deviceName) async {
    try {
      final healthData = await _healthService.fetchHealthData(deviceName);
      _healthData = HealthData.fromJson(healthData);
      notifyListeners();
      await HealthData.saveHealthData(_healthData!);
    } catch (e, stackTrace) {
      AppLogger.error('获取健康数据失败: $e', stackTrace: stackTrace, error: e);
    }
  }
}
