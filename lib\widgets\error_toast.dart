import 'package:flutter/material.dart';
import 'dart:async';

class ErrorToast extends StatefulWidget {
  final String errorMessage;
  final Duration duration;
  final VoidCallback? onDismiss;

  const ErrorToast({
    Key? key,
    required this.errorMessage,
    this.duration = const Duration(seconds: 5),
    this.onDismiss,
  }) : super(key: key);

  /// 显示错误提示
  static void show(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => ErrorToast(errorMessage: message),
    ).then((_) {
      // 弹窗关闭后的逻辑
    });
  }

  @override
  State<ErrorToast> createState() => _ErrorToastState();
}

class _ErrorToastState extends State<ErrorToast> {
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    _startTimer();
  }

  void _startTimer() {
    _timer = Timer(widget.duration, () {
      if (mounted) {
        Navigator.of(context).pop();
        if (widget.onDismiss != null) {
          widget.onDismiss!();
        }
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pop();
        if (widget.onDismiss != null) {
          widget.onDismiss!();
        }
      },
      child: Material(
        color: Colors.transparent,
        child: Center(
          child: Container(
            width: MediaQuery.of(context).size.width * 0.8,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.7),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Icon(
                  Icons.error_outline,
                  color: Colors.red,
                  size: 40,
                ),
                const SizedBox(height: 12),
                Text(
                  widget.errorMessage,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
