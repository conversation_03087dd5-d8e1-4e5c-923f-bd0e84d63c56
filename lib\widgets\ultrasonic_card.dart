import 'package:flutter/material.dart';
import 'package:pet_care/widgets/ultrasonic_slider.dart';

class UltrasonicCard extends StatelessWidget {
  final int index;
  final double frequency;
  final double duration;
  final bool isEnabled;
  final double minFrequency;
  final double maxFrequency;
  final Function(double) onFrequencyChanged;
  final Function(double) onDurationChanged;
  final Function(bool) onEnabledChanged;

  const UltrasonicCard({
    Key? key,
    required this.index,
    required this.frequency,
    required this.duration,
    required this.isEnabled,
    required this.minFrequency,
    required this.maxFrequency,
    required this.onFrequencyChanged,
    required this.onDurationChanged,
    required this.onEnabledChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: isEnabled ? 6 : 2,
      margin: EdgeInsets.symmetric(vertical: 4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isEnabled
              ? Theme.of(context).primaryColor
              : Colors.grey.withOpacity(0.3),
          width: isEnabled ? 2 : 1,
        ),
      ),
      child: AnimatedContainer(
        duration: Duration(milliseconds: 200),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: isEnabled
              ? LinearGradient(
                  colors: [
                    Theme.of(context).primaryColor.withOpacity(0.05),
                    Colors.white,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : LinearGradient(
                  colors: [
                    Colors.grey.withOpacity(0.02),
                    Colors.white,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 标题行
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: isEnabled
                              ? Theme.of(context).primaryColor
                              : Colors.grey.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: isEnabled
                              ? [
                                  BoxShadow(
                                    color: Theme.of(context)
                                        .primaryColor
                                        .withOpacity(0.3),
                                    blurRadius: 8,
                                    offset: Offset(0, 2),
                                  ),
                                ]
                              : null,
                        ),
                        child: Center(
                          child: Text(
                            '${index + 1}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 12),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '声波 ${index + 1}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 18,
                              color: isEnabled
                                  ? Theme.of(context).primaryColor
                                  : Colors.grey,
                            ),
                          ),
                          Text(
                            isEnabled ? '已启用' : '已禁用',
                            style: TextStyle(
                              fontSize: 12,
                              color: isEnabled
                                  ? Colors.green[600]
                                  : Colors.grey[500],
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  Transform.scale(
                    scale: 1.2,
                    child: Switch(
                      value: isEnabled,
                      activeColor: Theme.of(context).primaryColor,
                      activeTrackColor:
                          Theme.of(context).primaryColor.withOpacity(0.3),
                      inactiveThumbColor: Colors.grey[400],
                      inactiveTrackColor: Colors.grey[300],
                      onChanged: (value) {
                        onEnabledChanged(value);
                        if (!value) {
                          // 如果关闭开关，时间置为0
                          onDurationChanged(0);
                        }
                      },
                    ),
                  ),
                ],
              ),

              if (isEnabled) ...[
                SizedBox(height: 24),
                // 分隔线
                Container(
                  height: 1,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.transparent,
                        Theme.of(context).primaryColor.withOpacity(0.2),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 20),

                // 频率滑块
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.blue.withOpacity(0.1),
                    ),
                  ),
                  child: UltrasonicSlider(
                    title: '频率',
                    value: frequency,
                    min: minFrequency,
                    max: maxFrequency,
                    step: 1,
                    unit: 'kHz',
                    onChanged: onFrequencyChanged,
                    enabled: isEnabled,
                  ),
                ),

                SizedBox(height: 16),

                // 持续时间滑块
                Container(
                  padding: EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.orange.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.orange.withOpacity(0.1),
                    ),
                  ),
                  child: UltrasonicSlider(
                    title: '持续时间',
                    value: duration,
                    min: 0,
                    max: 60,
                    step: 0.1,
                    unit: 's',
                    onChanged: onDurationChanged,
                    enabled: isEnabled,
                  ),
                ),
              ] else ...[
                SizedBox(height: 16),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.info_outline,
                        size: 16,
                        color: Colors.grey[600],
                      ),
                      SizedBox(width: 8),
                      Text(
                        '启用开关以配置参数',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
