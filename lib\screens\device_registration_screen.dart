import 'dart:async';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../constants/constants.dart';
import '../providers/device_provider.dart';

class DeviceRegistrationScreen extends StatefulWidget {
  const DeviceRegistrationScreen({
    Key? key,
  }) : super(key: key);

  @override
  _DeviceRegistrationScreenState createState() =>
      _DeviceRegistrationScreenState();
}

class _DeviceRegistrationScreenState extends State<DeviceRegistrationScreen> {
  bool _isLoading = false;
  String? _errorMessage;
  String? _errorCode;
  int _tryCount = 0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _registerDevice();
    });
  }

  Future<void> _registerDevice() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
      _errorCode = null;
    });
    await Future.delayed(const Duration(seconds: 5));
    final deviceProvider = Provider.of<DeviceProvider>(context, listen: false);
    final result = await deviceProvider.registerDevice();
    if (result['Success']) {
      // 注册成功，返回首页
      if (mounted) {
        Navigator.of(context).pushReplacementNamed(Routes.home);
      }
    } else {
      // 如果重试3次都失败，则显示错误信息
      if (mounted && _tryCount >= 3) {
        await deviceProvider.removeDevice();
        setState(() {
          _isLoading = false;
          _errorMessage = result['errorMessage'];
          _errorCode = result['errorCode'];
        });
      } else {
        setState(() {
          _tryCount++;
        });
        _registerDevice();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('设备注册')),
      body: Center(
        child: _isLoading
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(height: 20),
                  Text('正在注册设备....'),
                ],
              )
            : _errorMessage != null
                ? Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        _errorMessage!,
                        style: TextStyle(color: Colors.red),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 20),
                      if (_errorCode != 'iot.register.fail')
                        ElevatedButton(
                          onPressed: _registerDevice,
                          child: Text('重试'),
                        )
                    ],
                  )
                : Container(),
      ),
    );
  }
}
