import 'dart:io'; // 用于平台判断
import 'package:permission_handler/permission_handler.dart';
// import 'package:flutter_open_app_settings/flutter_open_app_settings.dart'; // 用于打开设置页面
import 'app_logger.dart';

// PermissionsTool 用于处理应用所需的权限请求和状态检查
class PermissionsTool {
  // 请求存储权限
  Future<bool> requestStoragePermission() async {
    try {
      PermissionStatus status;
      if (Platform.isAndroid) {
        if (await Permission.manageExternalStorage.isGranted) {
          return true;
        }
        status = await Permission.manageExternalStorage.request();
      } else if (Platform.isIOS) {
        status = await Permission.photos.request();
      } else {
        // 其他平台处理
        return false;
      }

      if (status.isGranted) {
        return true;
      } else if (status.isDenied) {
        return await handlePermissionDenied(
            Platform.isAndroid
                ? Permission.manageExternalStorage
                : Permission.photos,
            '存储');
      } else {
        return false;
      }
    } catch (e, stackTrace) {
      AppLogger.error('请求存储权限时发生错误：$e', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  // 请求位置权限（用于蓝牙扫描）
  Future<bool> requestLocationPermission() async {
    try {
      PermissionStatus status;
      if (Platform.isAndroid) {
        status = await Permission.location.request();
      } else if (Platform.isIOS) {
        status = await Permission.locationWhenInUse.request();
      } else {
        // 其他平台处理
        return false;
      }

      if (status.isGranted) {
        return true;
      } else if (status.isDenied) {
        return await handlePermissionDenied(
            Platform.isAndroid
                ? Permission.location
                : Permission.locationWhenInUse,
            '位置');
      } else {
        return false;
      }
    } catch (e, stackTrace) {
      AppLogger.error('请求位置权限时发生错误：$e', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  // 请求蓝牙权限
  Future<bool> requestBluetoothPermission() async {
    try {
      if (Platform.isAndroid) {
        PermissionStatus bluetoothScan =
            await Permission.bluetoothScan.request();
        PermissionStatus bluetoothConnect =
            await Permission.bluetoothConnect.request();
        if (bluetoothScan.isGranted && bluetoothConnect.isGranted) {
          return true;
        } else if (bluetoothScan.isDenied || bluetoothConnect.isDenied) {
          return await handlePermissionDenied(Permission.bluetoothScan, '蓝牙');
        } else {
          return false;
        }
      } else if (Platform.isIOS) {
        PermissionStatus status = await Permission.bluetooth.request();
        if (status.isGranted) {
          return true;
        } else if (status.isDenied) {
          return await handlePermissionDenied(Permission.bluetooth, '蓝牙');
        } else {
          return false;
        }
      } else {
        // 其他平台处理
        return false;
      }
    } catch (e, stackTrace) {
      AppLogger.error('请求蓝牙权限时发生错误：$e', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  Future<bool> requestMicrophonePermission() async {
    try {
      // 请求麦克风权限
      var micStatus = await Permission.microphone.request();
      if (!micStatus.isGranted) {
        return false;
      } else {
        return true;
      }
    } catch (e, stackTrace) {
      AppLogger.error('请求麦克风权限时发生错误：$e', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  // 处理权限被拒绝的情况
  Future<bool> handlePermissionDenied(
      Permission permission, String permissionName) async {
    PermissionStatus status = await permission.status;
    if (status.isPermanentlyDenied) {
      // 权限被永久拒绝，无法请求，需打开设置页面
      AppLogger.warning('$permissionName 权限被永久拒绝，请在设置中手动开启。',
          error: Exception(permissionName), stackTrace: StackTrace.current);
      // 您可以在这里显示一个对话框，提示用户打开设置页面
      openAppSettings();
      return false;
    } else if (status.isDenied) {
      // 权限被拒绝，可以再次请求
      status = await permission.request();
      return status.isGranted;
    }
    return status.isGranted;
  }

  // 检查权限状态
  Future<bool> checkPermissionStatus(Permission permission) async {
    try {
      PermissionStatus status = await permission.status;
      return status.isGranted;
    } catch (e, stackTrace) {
      AppLogger.error('检查权限状态时发生错误：$e', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  // 请求所有必要权限
  Future<bool> requestAllPermissions() async {
    try {
      bool storageGranted = await requestStoragePermission();
      bool locationGranted = await requestLocationPermission();
      bool bluetoothGranted = await requestBluetoothPermission();
      bool microphoneGranted = await requestMicrophonePermission();

      return storageGranted &&
          locationGranted &&
          bluetoothGranted &&
          microphoneGranted;
    } catch (e, stackTrace) {
      AppLogger.error('请求所有权限时发生错误：$e', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  // 请求通知权限
  Future<bool> requestNotificationPermission() async {
    return await _requestPermission(Permission.notification);
  }

  // 通用权限请求方法
  Future<bool> _requestPermission(Permission permission) async {
    try {
      PermissionStatus status = await permission.request();
      if (status.isGranted) {
        return true;
      } else if (status.isPermanentlyDenied) {
        // 引导用户到应用设置页面开启权限
        openAppSettings();
        return false;
      } else {
        return false;
      }
    } catch (e, stackTrace) {
      AppLogger.error('请求权限时发生错误：$e', error: e, stackTrace: stackTrace);
      return false;
    }
  }
}
