import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../widgets/time_period_selector.dart';
import '../../widgets/chart_view_selector.dart';
import '../../services/mock_data_service.dart';
import '../../models/extended_health_data.dart';
import '../../constants/health_detection.dart';

/// 健康监测统计界面
class HealthDetectionStatisticsScreen extends StatefulWidget {
  @override
  _HealthDetectionStatisticsScreenState createState() => _HealthDetectionStatisticsScreenState();
}

class _HealthDetectionStatisticsScreenState extends State<HealthDetectionStatisticsScreen> {
  TimePeriod _selectedPeriod = TimePeriod.day;
  ChartViewType _selectedChartType = ChartViewType.scatter;
  DateTime _currentDate = DateTime.now();
  List<ExtendedHealthData> _healthData = [];
  bool _isLoading = true;

  // 用于散点图游标线功能
  double? _touchedX;

  // 用于日模式滑块控制
  double _sliderValue = 0.0; // 滑块位置，范围0-47



  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<ExtendedHealthData> data;
      switch (_selectedPeriod) {
        case TimePeriod.day:
          data = MockDataService.generateDayData(date: _currentDate);
          break;
        case TimePeriod.week:
          data = MockDataService.generateWeekData(startDate: _currentDate);
          break;
        case TimePeriod.month:
          data = MockDataService.generateMonthData(month: _currentDate);
          break;
      }
      
      setState(() {
        _healthData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载数据失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('健康监测统计'),
        backgroundColor: Colors.red.shade50,
      ),
      body: Column(
        children: [
          // 时间段选择器
          TimePeriodSelectorWithNavigation(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
                // 根据时间模式自动切换图表类型
                final availableTypes = ChartTypePresets.getHealthDetectionStatistics(period);
                if (availableTypes.isNotEmpty && !availableTypes.contains(_selectedChartType)) {
                  _selectedChartType = availableTypes.first;
                }
              });
              _loadData();
            },
            currentDate: _currentDate,
            onDateChanged: (date) {
              setState(() {
                _currentDate = date;
              });
              _loadData();
            },
          ),
          
          // 图表类型选择器
          ChartViewSelector(
            availableTypes: ChartTypePresets.getHealthDetectionStatistics(_selectedPeriod),
            selectedType: _selectedChartType,
            onTypeChanged: (type) {
              setState(() {
                _selectedChartType = type;
              });
            },
          ),
          
          // 图表区域
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator())
                : Padding(
                    padding: EdgeInsets.all(16),
                    child: Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _selectedChartType == ChartViewType.scatter ? '健康异常监测分布' : '健康监测统计',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            SizedBox(height: 16),
                            Expanded(
                              child: _buildChart(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
          ),
          
          // 统计信息
          if (!_isLoading) _buildStatisticsInfo(),
        ],
      ),
    );
  }

  /// 构建图表
  Widget _buildChart() {
    if (_healthData.isEmpty) {
      return Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    switch (_selectedChartType) {
      case ChartViewType.scatter:
        return _buildScatterChart();
      case ChartViewType.horizontalBar:
        return _buildHorizontalBarChart();
      default:
        return _buildScatterChart();
    }
  }

  /// 构建散点图（健康监测项目分布）
  Widget _buildScatterChart() {
    final scatterSpots = <ScatterSpot>[];
    final detectionColors = <HealthDetection, Color>{};

    // 为每种监测项目分配颜色
    for (final detection in HealthDetection.values) {
      detectionColors[detection] = Color(int.parse('0xFF${detection.colorHex.substring(1)}'));
    }

    // 为日模式生成48个30分钟间隔的时间点（00:00-23:30）
    if (_selectedPeriod == TimePeriod.day) {
      for (int timeIndex = 0; timeIndex < 48; timeIndex++) {
        // 查找对应时间段的数据
        final targetMinutes = timeIndex * 30;
        final targetHour = targetMinutes ~/ 60;
        final targetMinute = targetMinutes % 60;

        // 在现有数据中查找匹配的时间点
        for (int i = 0; i < _healthData.length; i++) {
          final data = _healthData[i];
          final dataHour = data.datetime.hour;
          final dataMinute = data.datetime.minute;

          // 检查是否在30分钟时间窗口内
          if (dataHour == targetHour && (dataMinute - targetMinute).abs() <= 15) {
            for (final detection in data.healthDetections) {
              scatterSpots.add(
                ScatterSpot(
                  timeIndex.toDouble(),
                  HealthDetection.values.indexOf(detection).toDouble(),
                  dotPainter: FlDotCirclePainter(
                    radius: 3,
                    color: detectionColors[detection]!,
                  ),
                ),
              );
            }
            break; // 找到匹配的数据后跳出内层循环
          }
        }
      }
    } else {
      // 周模式和月模式保持原有逻辑
      for (int i = 0; i < _healthData.length; i++) {
        final data = _healthData[i];
        for (final detection in data.healthDetections) {
          scatterSpots.add(
            ScatterSpot(
              i.toDouble(),
              HealthDetection.values.indexOf(detection).toDouble(),
              dotPainter: FlDotCirclePainter(
                radius: 3,
                color: detectionColors[detection]!,
              ),
            ),
          );
        }
      }
    }

    if (scatterSpots.isEmpty) {
      return Center(
        child: Text(
          '该时间段内无健康异常监测',
          style: TextStyle(
            fontSize: 16,
            color: Colors.green.shade600,
          ),
        ),
      );
    }

    return Column(
      children: [
        Expanded(
          child: AspectRatio(
            aspectRatio: 2.0, // 设置宽高比为2:1
            child: ScatterChart(
                ScatterChartData(
                  // 设置坐标轴范围
                  minX: 0,
                  maxX: 47, // 24小时 * 2 (30分钟间隔) - 1 = 47
                  minY: -0.5,
                  maxY: HealthDetection.values.length - 0.5,
                  gridData: FlGridData(
                    show: true,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: Colors.grey.shade300,
                        strokeWidth: 1,
                        dashArray: [5, 5], // 设置虚线样式
                      );
                    },
                    getDrawingVerticalLine: (value) {
                      // 绘制垂直游标线
                      if (_touchedX != null && (value - _touchedX!).abs() < 0.5) {
                        return FlLine(
                          color: Colors.blue.shade400,
                          strokeWidth: 2,
                        );
                      }
                      return FlLine(
                        color: Colors.grey.shade300,
                        strokeWidth: 1,
                        dashArray: [5, 5],
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
                        interval: 12.0, // 每12个点显示一次标签（6小时间隔：00:00, 06:00, 12:00, 18:00）
                        getTitlesWidget: (value, meta) {
                          return Padding(
                            padding: const EdgeInsets.only(top: 8.0), // 向下推进横坐标值
                            child: Text(
                              _getBottomTitle(value.toInt()),
                              style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                            ),
                          );
                        },
                      ),
                    ),
                    rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏右侧标题
                    topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: false), // 去掉坐标轴外框
                  scatterTouchData: ScatterTouchData(
                    enabled: true,
                    handleBuiltInTouches: true,
                    mouseCursorResolver: (FlTouchEvent event, ScatterTouchResponse? response) {
                      return SystemMouseCursors.click;
                    },
                    touchCallback: (FlTouchEvent event, ScatterTouchResponse? response) {
                      if (event is FlTapUpEvent || event is FlPanUpdateEvent || event is FlPointerHoverEvent) {
                        // 处理触摸事件，更新游标位置
                        if (response?.touchedSpot != null) {
                          final touchedSpot = response!.touchedSpot!;
                          final touchedX = touchedSpot.spot.x;
                          setState(() {
                            _touchedX = touchedX;
                            _sliderValue = touchedX.clamp(0, 47); // 同步更新滑块位置
                          });
                        } else if (event is FlPointerHoverEvent) {
                          // 处理鼠标悬停事件，即使没有触摸到具体的点
                          final localPosition = event.localPosition;
                          // 这里需要将屏幕坐标转换为图表坐标
                          // 简化处理：基于鼠标位置估算X坐标
                          final chartWidth = 400.0; // 估算图表宽度
                          final xRatio = localPosition.dx / chartWidth;
                          final estimatedX = xRatio * 47; // 47是maxX
                          setState(() {
                            _touchedX = estimatedX.clamp(0, 47);
                            _sliderValue = _touchedX!; // 同步更新滑块位置
                          });
                        }
                      } else if (event is FlPanEndEvent || event is FlPointerExitEvent) {
                        // 不清除游标线，保持滑块控制的游标位置
                        // setState(() {
                        //   _touchedX = null;
                        // });
                      }
                    },
                    touchTooltipData: ScatterTouchTooltipData(
                      getTooltipItems: (ScatterSpot touchedBarSpot) {
                        return _getTooltipForTimeIndex(touchedBarSpot.x.toInt());
                      },
                    ),
                  ),
                  scatterSpots: scatterSpots,
                ),
              ),
            ),
          ),

        // 日模式当前时间点信息显示
        if (_selectedPeriod == TimePeriod.day && _touchedX != null)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Container(
              padding: EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.info_outline, size: 16, color: Colors.blue.shade600),
                  SizedBox(width: 8),
                  Text(
                    _getCurrentTimeTooltipText(),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue.shade800,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),

        SizedBox(height: 16),

        // 日模式滑块控制器（仅在日模式下显示）
        if (_selectedPeriod == TimePeriod.day)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Text(
                  '00:00',
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
                Expanded(
                  child: SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      trackHeight: 4.0,
                      thumbShape: RoundSliderThumbShape(enabledThumbRadius: 8.0),
                      overlayShape: RoundSliderOverlayShape(overlayRadius: 16.0),
                      activeTrackColor: Colors.blue.shade400,
                      inactiveTrackColor: Colors.grey.shade300,
                      thumbColor: Colors.blue.shade600,
                      overlayColor: Colors.blue.shade200,
                    ),
                    child: Slider(
                      value: _sliderValue,
                      min: 0.0,
                      max: 47.0,
                      divisions: 47,
                      onChanged: (value) {
                        setState(() {
                          _sliderValue = value;
                          _touchedX = value; // 同步更新游标位置
                        });
                      },
                    ),
                  ),
                ),
                Text(
                  '24:00',
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
              ],
            ),
          ),

        SizedBox(height: 16),
        // 底部图例
        _buildBottomLegend(detectionColors),
      ],
    );
  }

  /// 构建水平条形图（各类健康监测的出现次数）
  Widget _buildHorizontalBarChart() {
    // 统计各类健康监测的出现次数
    final detectionCounts = <HealthDetection, int>{};
    for (final data in _healthData) {
      for (final detection in data.healthDetections) {
        detectionCounts[detection] = (detectionCounts[detection] ?? 0) + 1;
      }
    }

    // 确保所有10种监测项目都有数据（没有的设为0）
    for (final detection in HealthDetection.values) {
      detectionCounts.putIfAbsent(detection, () => 0);
    }

    if (detectionCounts.values.every((count) => count == 0)) {
      return Center(
        child: Text(
          '该时间段内无健康异常监测',
          style: TextStyle(
            fontSize: 16,
            color: Colors.green.shade600,
          ),
        ),
      );
    }

    // 按照固定顺序排列（从上到下）：发热、呼吸异常、运动不稳、步态不对称、步态规律性下降、夜醒、睡眠不足、运动复杂度异常、活动模式改变、kcal下降
    final sortedEntries = HealthDetection.values.map((detection) {
      return MapEntry(detection, detectionCounts[detection]!);
    }).toList();

    // 反转顺序，使发热在顶部
    final reversedEntries = sortedEntries.reversed.toList();

    // 计算最大值用于比例计算
    final maxCount = reversedEntries.map((e) => e.value).reduce((a, b) => a > b ? a : b);

    return Expanded(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8), // 减少padding
        child: Column(
          children: reversedEntries.map((entry) {
            final detection = entry.key;
            final count = entry.value;
            final color = Color(int.parse('0xFF${detection.colorHex.substring(1)}'));
            final percentage = maxCount > 0 ? count / maxCount : 0.0;

            return Expanded(
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 1.5), // 增加条形图间距，提高可读性
                child: Row(
                  children: [
                    // 条形图区域
                    Expanded(
                      child: Stack(
                        children: [
                          // 背景条
                          Container(
                            height: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          // 数据条（彩色条纹，不包含标签）
                          FractionallySizedBox(
                            widthFactor: percentage,
                            child: Container(
                              height: double.infinity,
                              decoration: BoxDecoration(
                                color: color,
                                borderRadius: BorderRadius.circular(4),
                              ),
                            ),
                          ),
                          // 标签（在浅灰色背景的最左侧，最顶层显示）
                          Positioned(
                            left: 8,
                            top: 0,
                            bottom: 0,
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Text(
                                detection.displayName, // 在浅灰色背景最左侧的标签
                                style: TextStyle(
                                  color: Colors.white, // 白色文字，在最顶层显示
                                  fontSize: 11,
                                  fontWeight: FontWeight.bold,
                                ),
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 8),
                    // 外部数值显示（右侧）
                    SizedBox(
                      width: 30,
                      child: Text(
                        '$count',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }



  /// 构建底部图例
  Widget _buildBottomLegend(Map<HealthDetection, Color> detectionColors) {
    return Wrap(
      spacing: 8.0,
      runSpacing: 4.0,
      children: HealthDetection.values.map((detection) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 8, // 缩小到30%（原来是12）
              height: 8,
              decoration: BoxDecoration(
                color: detectionColors[detection]!,
                shape: BoxShape.circle,
              ),
            ),
            SizedBox(width: 4),
            Text(
              detection.displayName,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade700,
              ),
            ),
          ],
        );
      }).toList(),
    );
  }



  /// 获取底部标题
  String _getBottomTitle(int index) {
    switch (_selectedPeriod) {
      case TimePeriod.day:
        // 日模式：显示00:00~24:00，30分钟间隔
        // index对应30分钟间隔的时间点：0=00:00, 1=00:30, 2=01:00, ...
        final totalMinutes = index * 30; // 30分钟间隔
        final hour = (totalMinutes ~/ 60) % 24; // 确保小时在0-23范围内
        final minute = totalMinutes % 60;
        return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
      case TimePeriod.week:
        if (index >= _healthData.length) return '';
        final data = _healthData[index];
        return '${data.datetime.month}/${data.datetime.day}';
      case TimePeriod.month:
        if (index >= _healthData.length) return '';
        final data = _healthData[index];
        return '${data.datetime.day}日';
    }
  }

  /// 构建统计信息
  Widget _buildStatisticsInfo() {
    // 统计健康监测信息
    final allDetections = _healthData.expand((data) => data.healthDetections).toList();
    final dangerousCount = allDetections.where((d) => d.isDangerous).length;
    final totalCount = allDetections.length;
    final feverCount = allDetections.where((d) => d == HealthDetection.fever).length;
    
    final mostCommonDetection = _getMostCommonDetection(allDetections);

    return Container(
      padding: EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('总检测数', totalCount.toString(), Colors.blue),
                  _buildStatItem('危险异常', dangerousCount.toString(), Colors.red),
                  _buildStatItem('发热次数', feverCount.toString(), Colors.orange),
                ],
              ),
              SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('最常见', mostCommonDetection?.displayName ?? '无', Colors.purple),
                  _buildStatItem('健康状态', _getHealthStatus(dangerousCount, totalCount), _getHealthStatusColor(dangerousCount, totalCount)),
                  _buildStatItem('监测天数', '${_selectedPeriod.displayName}', Colors.grey),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取最常见的健康监测
  HealthDetection? _getMostCommonDetection(List<HealthDetection> detections) {
    if (detections.isEmpty) return null;
    
    final detectionCounts = <HealthDetection, int>{};
    for (final detection in detections) {
      detectionCounts[detection] = (detectionCounts[detection] ?? 0) + 1;
    }
    
    return detectionCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  /// 获取健康状态描述
  String _getHealthStatus(int dangerousCount, int totalCount) {
    if (totalCount == 0) return '良好';
    
    final dangerousRatio = dangerousCount / totalCount;
    if (dangerousRatio == 0) return '良好';
    if (dangerousRatio < 0.1) return '注意';
    if (dangerousRatio < 0.3) return '警告';
    return '危险';
  }

  /// 获取健康状态颜色
  Color _getHealthStatusColor(int dangerousCount, int totalCount) {
    if (totalCount == 0) return Colors.green;
    
    final dangerousRatio = dangerousCount / totalCount;
    if (dangerousRatio == 0) return Colors.green;
    if (dangerousRatio < 0.1) return Colors.yellow.shade700;
    if (dangerousRatio < 0.3) return Colors.orange;
    return Colors.red;
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }

  /// 获取指定时间索引的tooltip信息
  ScatterTooltipItem? _getTooltipForTimeIndex(int index) {
    // 计算时间（30分钟间隔）
    final hour = (index * 30) ~/ 60;
    final minute = (index * 30) % 60;
    final timeRange = '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';

    // 获取该时间点的所有监测项目
    if (_selectedPeriod == TimePeriod.day) {
      // 日模式：根据30分钟间隔查找对应的数据
      final targetMinutes = index * 30;
      final targetHour = targetMinutes ~/ 60;
      final targetMinute = targetMinutes % 60;

      final matchingDetections = <HealthDetection>[];
      for (final data in _healthData) {
        final dataHour = data.datetime.hour;
        final dataMinute = data.datetime.minute;

        // 检查是否在30分钟时间窗口内
        if (dataHour == targetHour && (dataMinute - targetMinute).abs() <= 15) {
          matchingDetections.addAll(data.healthDetections);
        }
      }

      if (matchingDetections.isEmpty) {
        return ScatterTooltipItem(
          '$timeRange\n无异常监测',
          textStyle: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        );
      }

      final detectionNames = matchingDetections.map((d) => d.displayName).join('、');
      return ScatterTooltipItem(
        '$timeRange\n$detectionNames',
        textStyle: const TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 12,
        ),
      );
    } else {
      // 周模式和月模式：使用原有逻辑
      if (index >= 0 && index < _healthData.length) {
        final data = _healthData[index];
        final detections = data.healthDetections;

        if (detections.isEmpty) {
          return ScatterTooltipItem(
            '$timeRange\n无异常监测',
            textStyle: const TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          );
        }

        final detectionNames = detections.map((d) => d.displayName).join('、');
        return ScatterTooltipItem(
          '$timeRange\n$detectionNames',
          textStyle: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 12,
          ),
        );
      }
    }

    return ScatterTooltipItem(
      timeRange,
      textStyle: const TextStyle(
        color: Colors.white,
        fontWeight: FontWeight.bold,
        fontSize: 12,
      ),
    );
  }

  /// 获取当前滑块位置的tooltip文本
  String _getCurrentTimeTooltipText() {
    if (_touchedX == null) return '';

    final index = _touchedX!.round();
    final hour = (index * 30) ~/ 60;
    final minute = (index * 30) % 60;
    final timeRange = '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';

    // 获取该时间点的所有监测项目
    final targetMinutes = index * 30;
    final targetHour = targetMinutes ~/ 60;
    final targetMinute = targetMinutes % 60;

    final matchingDetections = <HealthDetection>[];
    for (final data in _healthData) {
      final dataHour = data.datetime.hour;
      final dataMinute = data.datetime.minute;

      // 检查是否在30分钟时间窗口内
      if (dataHour == targetHour && (dataMinute - targetMinute).abs() <= 15) {
        matchingDetections.addAll(data.healthDetections);
      }
    }

    if (matchingDetections.isEmpty) {
      return '$timeRange - 无异常监测';
    }

    final detectionNames = matchingDetections.map((d) => d.displayName).join('、');
    return '$timeRange - $detectionNames';
  }
}
