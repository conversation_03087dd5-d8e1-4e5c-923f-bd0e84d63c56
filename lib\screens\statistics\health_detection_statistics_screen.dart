import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../widgets/time_period_selector.dart';
import '../../widgets/chart_view_selector.dart';
import '../../services/mock_data_service.dart';
import '../../models/extended_health_data.dart';
import '../../constants/health_detection.dart';

/// 健康监测统计界面
class HealthDetectionStatisticsScreen extends StatefulWidget {
  @override
  _HealthDetectionStatisticsScreenState createState() => _HealthDetectionStatisticsScreenState();
}

class _HealthDetectionStatisticsScreenState extends State<HealthDetectionStatisticsScreen> {
  TimePeriod _selectedPeriod = TimePeriod.day;
  ChartViewType _selectedChartType = ChartViewType.scatter;
  DateTime _currentDate = DateTime.now();
  List<ExtendedHealthData> _healthData = [];
  bool _isLoading = true;

  // 用于散点图游标线功能
  double? _touchedX;



  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<ExtendedHealthData> data;
      switch (_selectedPeriod) {
        case TimePeriod.day:
          data = MockDataService.generateDayData(date: _currentDate);
          break;
        case TimePeriod.week:
          data = MockDataService.generateWeekData(startDate: _currentDate);
          break;
        case TimePeriod.month:
          data = MockDataService.generateMonthData(month: _currentDate);
          break;
      }
      
      setState(() {
        _healthData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('加载数据失败: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('健康监测统计'),
        backgroundColor: Colors.red.shade50,
      ),
      body: Column(
        children: [
          // 时间段选择器
          TimePeriodSelectorWithNavigation(
            selectedPeriod: _selectedPeriod,
            onPeriodChanged: (period) {
              setState(() {
                _selectedPeriod = period;
                // 根据时间模式自动切换图表类型
                final availableTypes = ChartTypePresets.getHealthDetectionStatistics(period);
                if (availableTypes.isNotEmpty && !availableTypes.contains(_selectedChartType)) {
                  _selectedChartType = availableTypes.first;
                }
              });
              _loadData();
            },
            currentDate: _currentDate,
            onDateChanged: (date) {
              setState(() {
                _currentDate = date;
              });
              _loadData();
            },
          ),
          
          // 图表类型选择器
          ChartViewSelector(
            availableTypes: ChartTypePresets.getHealthDetectionStatistics(_selectedPeriod),
            selectedType: _selectedChartType,
            onTypeChanged: (type) {
              setState(() {
                _selectedChartType = type;
              });
            },
          ),
          
          // 图表区域
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator())
                : Padding(
                    padding: EdgeInsets.all(16),
                    child: Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              _selectedChartType == ChartViewType.scatter ? '健康异常监测分布' : '健康监测统计',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            SizedBox(height: 16),
                            Expanded(
                              child: _buildChart(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
          ),
          
          // 统计信息
          if (!_isLoading) _buildStatisticsInfo(),
        ],
      ),
    );
  }

  /// 构建图表
  Widget _buildChart() {
    if (_healthData.isEmpty) {
      return Center(
        child: Text(
          '暂无数据',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey.shade600,
          ),
        ),
      );
    }

    switch (_selectedChartType) {
      case ChartViewType.scatter:
        return _buildScatterChart();
      case ChartViewType.horizontalBar:
        return _buildHorizontalBarChart();
      default:
        return _buildScatterChart();
    }
  }

  /// 构建散点图（健康监测项目分布）
  Widget _buildScatterChart() {
    final scatterSpots = <ScatterSpot>[];
    final detectionColors = <HealthDetection, Color>{};

    // 为每种监测项目分配颜色
    for (final detection in HealthDetection.values) {
      detectionColors[detection] = Color(int.parse('0xFF${detection.colorHex.substring(1)}'));
    }

    for (int i = 0; i < _healthData.length; i++) {
      final data = _healthData[i];
      for (final detection in data.healthDetections) {
        scatterSpots.add(
          ScatterSpot(
            i.toDouble(),
            HealthDetection.values.indexOf(detection).toDouble(),
            dotPainter: FlDotCirclePainter(
              radius: 3, // 缩小到原来的30%（原来是6-8，现在是3）
              color: detectionColors[detection]!,
            ),
          ),
        );
      }
    }

    if (scatterSpots.isEmpty) {
      return Center(
        child: Text(
          '该时间段内无健康异常监测',
          style: TextStyle(
            fontSize: 16,
            color: Colors.green.shade600,
          ),
        ),
      );
    }

    return Column(
      children: [
        Expanded(
          child: AspectRatio(
            aspectRatio: 2.0, // 设置宽高比为2:1
            child: ScatterChart(
                ScatterChartData(
                  gridData: FlGridData(
                    show: true,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: Colors.grey.shade300,
                        strokeWidth: 1,
                        dashArray: [5, 5], // 设置虚线样式
                      );
                    },
                    getDrawingVerticalLine: (value) {
                      // 绘制垂直游标线
                      if (_touchedX != null && (value - _touchedX!).abs() < 0.5) {
                        return FlLine(
                          color: Colors.blue.shade400,
                          strokeWidth: 2,
                        );
                      }
                      return FlLine(
                        color: Colors.grey.shade300,
                        strokeWidth: 1,
                        dashArray: [5, 5],
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏左侧标题
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 40, // 增加底部预留空间，拉开与绘图区域的距离
                        interval: 2.0, // 30分钟间隔：每小时2个数据点，显示每30分钟
                        getTitlesWidget: (value, meta) {
                          return Padding(
                            padding: const EdgeInsets.only(top: 8.0), // 向下推进横坐标值
                            child: Text(
                              _getBottomTitle(value.toInt()),
                              style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
                            ),
                          );
                        },
                      ),
                    ),
                    rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)), // 隐藏右侧标题
                    topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: false), // 去掉坐标轴外框
                  scatterTouchData: ScatterTouchData(
                    enabled: true,
                    handleBuiltInTouches: true,
                    mouseCursorResolver: (FlTouchEvent event, ScatterTouchResponse? response) {
                      return SystemMouseCursors.click;
                    },
                    touchCallback: (FlTouchEvent event, ScatterTouchResponse? response) {
                      if (event is FlTapUpEvent || event is FlPanUpdateEvent || event is FlPointerHoverEvent) {
                        // 处理触摸事件，更新游标位置
                        if (response?.touchedSpot != null) {
                          final touchedSpot = response!.touchedSpot!;
                          final touchedX = touchedSpot.spot.x;
                          setState(() {
                            _touchedX = touchedX;
                          });
                        }
                      } else if (event is FlPanEndEvent || event is FlPointerExitEvent) {
                        // 清除游标线
                        setState(() {
                          _touchedX = null;
                        });
                      }
                    },
                    touchTooltipData: ScatterTouchTooltipData(
                      getTooltipItems: (ScatterSpot touchedBarSpot) {
                        final index = touchedBarSpot.x.toInt();

                        // 计算时间（15分钟间隔）
                        final hour = (index * 15) ~/ 60;
                        final minute = (index * 15) % 60;
                        final timeRange = '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';

                        // 获取该时间点的所有监测项目
                        if (index >= 0 && index < _healthData.length) {
                          final data = _healthData[index];
                          final detections = data.healthDetections;

                          if (detections.isEmpty) {
                            return ScatterTooltipItem(
                              '$timeRange\n无异常监测',
                              textStyle: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 12,
                              ),
                            );
                          }

                          final detectionNames = detections.map((d) => d.displayName).join('、');
                          return ScatterTooltipItem(
                            '$timeRange\n$detectionNames',
                            textStyle: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          );
                        }

                        return ScatterTooltipItem(
                          timeRange,
                          textStyle: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 12,
                          ),
                        );
                      },
                    ),
                  ),
                  scatterSpots: scatterSpots,
                ),
              ),
            ),
          ),

        SizedBox(height: 16),
        // 底部图例
        _buildBottomLegend(detectionColors),
      ],
    );
  }

  /// 构建水平条形图（各类健康监测的出现次数）
  Widget _buildHorizontalBarChart() {
    // 统计各类健康监测的出现次数
    final detectionCounts = <HealthDetection, int>{};
    for (final data in _healthData) {
      for (final detection in data.healthDetections) {
        detectionCounts[detection] = (detectionCounts[detection] ?? 0) + 1;
      }
    }

    // 确保所有10种监测项目都有数据（没有的设为0）
    for (final detection in HealthDetection.values) {
      detectionCounts.putIfAbsent(detection, () => 0);
    }

    if (detectionCounts.values.every((count) => count == 0)) {
      return Center(
        child: Text(
          '该时间段内无健康异常监测',
          style: TextStyle(
            fontSize: 16,
            color: Colors.green.shade600,
          ),
        ),
      );
    }

    // 按照固定顺序排列（从上到下）：发热、呼吸异常、运动不稳、步态不对称、步态规律性下降、夜醒、睡眠不足、运动复杂度异常、活动模式改变、kcal下降
    final sortedEntries = HealthDetection.values.map((detection) {
      return MapEntry(detection, detectionCounts[detection]!);
    }).toList();

    // 反转顺序，使发热在顶部
    final reversedEntries = sortedEntries.reversed.toList();

    // 计算最大值用于比例计算
    final maxCount = reversedEntries.map((e) => e.value).reduce((a, b) => a > b ? a : b);

    return Expanded(
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        child: Column(
          children: reversedEntries.map((entry) {
            final detection = entry.key;
            final count = entry.value;
            final color = Color(int.parse('0xFF${detection.colorHex.substring(1)}'));
            final percentage = maxCount > 0 ? count / maxCount : 0.0;

            return Expanded(
              child: Container(
                margin: EdgeInsets.symmetric(vertical: 1), // 最小间距
                child: Row(
                  children: [
                    // 外部数值显示（左侧）
                    SizedBox(
                      width: 30,
                      child: Text(
                        '$count',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                    SizedBox(width: 8),
                    // 条形图区域
                    Expanded(
                      child: Stack(
                        children: [
                          // 背景条
                          Container(
                            height: double.infinity,
                            decoration: BoxDecoration(
                              color: Colors.grey.shade200,
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                          // 数据条
                          FractionallySizedBox(
                            widthFactor: percentage,
                            child: Container(
                              height: double.infinity,
                              decoration: BoxDecoration(
                                color: color,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: count > 0 ? Align(
                                alignment: Alignment.centerLeft,
                                child: Padding(
                                  padding: EdgeInsets.only(left: 8),
                                  child: Text(
                                    detection.displayName, // 内部标签，左对齐
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontSize: 11,
                                      fontWeight: FontWeight.bold,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                              ) : null,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }



  /// 构建底部图例
  Widget _buildBottomLegend(Map<HealthDetection, Color> detectionColors) {
    return Wrap(
      spacing: 8.0,
      runSpacing: 4.0,
      children: HealthDetection.values.map((detection) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 8, // 缩小到30%（原来是12）
              height: 8,
              decoration: BoxDecoration(
                color: detectionColors[detection]!,
                shape: BoxShape.circle,
              ),
            ),
            SizedBox(width: 4),
            Text(
              detection.displayName,
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey.shade700,
              ),
            ),
          ],
        );
      }).toList(),
    );
  }



  /// 获取底部标题
  String _getBottomTitle(int index) {
    if (index >= _healthData.length) return '';
    
    final data = _healthData[index];
    switch (_selectedPeriod) {
      case TimePeriod.day:
        return '${data.datetime.hour}:${data.datetime.minute.toString().padLeft(2, '0')}';
      case TimePeriod.week:
        return '${data.datetime.month}/${data.datetime.day}';
      case TimePeriod.month:
        return '${data.datetime.day}日';
    }
  }

  /// 构建统计信息
  Widget _buildStatisticsInfo() {
    // 统计健康监测信息
    final allDetections = _healthData.expand((data) => data.healthDetections).toList();
    final dangerousCount = allDetections.where((d) => d.isDangerous).length;
    final totalCount = allDetections.length;
    final feverCount = allDetections.where((d) => d == HealthDetection.fever).length;
    
    final mostCommonDetection = _getMostCommonDetection(allDetections);

    return Container(
      padding: EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('总检测数', totalCount.toString(), Colors.blue),
                  _buildStatItem('危险异常', dangerousCount.toString(), Colors.red),
                  _buildStatItem('发热次数', feverCount.toString(), Colors.orange),
                ],
              ),
              SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildStatItem('最常见', mostCommonDetection?.displayName ?? '无', Colors.purple),
                  _buildStatItem('健康状态', _getHealthStatus(dangerousCount, totalCount), _getHealthStatusColor(dangerousCount, totalCount)),
                  _buildStatItem('监测天数', '${_selectedPeriod.displayName}', Colors.grey),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取最常见的健康监测
  HealthDetection? _getMostCommonDetection(List<HealthDetection> detections) {
    if (detections.isEmpty) return null;
    
    final detectionCounts = <HealthDetection, int>{};
    for (final detection in detections) {
      detectionCounts[detection] = (detectionCounts[detection] ?? 0) + 1;
    }
    
    return detectionCounts.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  /// 获取健康状态描述
  String _getHealthStatus(int dangerousCount, int totalCount) {
    if (totalCount == 0) return '良好';
    
    final dangerousRatio = dangerousCount / totalCount;
    if (dangerousRatio == 0) return '良好';
    if (dangerousRatio < 0.1) return '注意';
    if (dangerousRatio < 0.3) return '警告';
    return '危险';
  }

  /// 获取健康状态颜色
  Color _getHealthStatusColor(int dangerousCount, int totalCount) {
    if (totalCount == 0) return Colors.green;
    
    final dangerousRatio = dangerousCount / totalCount;
    if (dangerousRatio == 0) return Colors.green;
    if (dangerousRatio < 0.1) return Colors.yellow.shade700;
    if (dangerousRatio < 0.3) return Colors.orange;
    return Colors.red;
  }

  /// 构建统计项
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
        SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
      ],
    );
  }
}
