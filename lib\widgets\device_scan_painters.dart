import 'dart:math';
import 'package:flutter/material.dart';

// 雷达画图 - 波纹绘制器
class CirclesPainter extends CustomPainter {
  final double animationValue;
  final Color waveColor;
  final int circleCount;
  final double strokeWidth;

  // 提供默认值，并允许自定义颜色、波纹数量和线条粗细
  CirclesPainter(
    this.animationValue, {
    this.waveColor = Colors.orangeAccent,
    this.circleCount = 5,
    this.strokeWidth = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..isAntiAlias = true; // 抗锯齿，提高视觉效果

    final double maxRadius = size.width * 0.5;
    final Offset center = size.center(Offset.zero);

    // 预先计算一些常量，避免循环内重复计算
    final double step = 1.0 / circleCount;

    for (int i = 0; i < circleCount; i++) {
      // 计算每个波纹的当前进度，使其均匀分布在整个动画周期中
      double progress = (animationValue + i * step) % 1.0;

      // 计算波纹半径，从0开始向外扩散到最大半径
      double radius = maxRadius * progress;

      // 使用二次函数使透明度变化更加自然
      // 刚开始时透明度快速降低，之后降低速度变缓
      double opacity = pow(1.0 - progress, 2) * 0.7;
      opacity = opacity.clamp(0.15, 1.0);

      paint.color = waveColor.withOpacity(opacity);

      // 只有当圆半径足够大时才绘制（避免绘制过小的圆）
      if (radius > 2.0) {
        canvas.drawCircle(center, radius, paint);
      }
    }
  }

  @override
  bool shouldRepaint(covariant CirclesPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue ||
        oldDelegate.waveColor != waveColor ||
        oldDelegate.circleCount != circleCount ||
        oldDelegate.strokeWidth != strokeWidth;
  }
}

// 扫描线绘制器
class ScanLinePainter extends CustomPainter {
  final int scanAngle;
  final List<Color> gradientColors;

  // 提供默认值，并允许自定义扫描角度和渐变颜色
  ScanLinePainter({
    this.scanAngle = 20,
    this.gradientColors = const [
      Colors.red,
      Colors.redAccent,
    ],
  });

  @override
  void paint(Canvas canvas, Size size) {
    final Offset center = size.center(Offset.zero);
    final double radius = size.width * 0.5;

    // 创建一个渐变画笔
    final Paint gradientPaint = Paint()
      ..shader = SweepGradient(
        startAngle: 0,
        endAngle: pi / scanAngle,
        colors: [
          gradientColors[0].withOpacity(0.05),
          gradientColors[1].withOpacity(0.5),
        ],
        tileMode: TileMode.clamp,
      ).createShader(Rect.fromCircle(center: center, radius: radius))
      ..style = PaintingStyle.fill
      ..isAntiAlias = true; // 抗锯齿，提高视觉效果

    // 绘制扫描弧线
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      0, // 起始角度
      pi / scanAngle, // 扫角度
      true, // 使用中心点
      gradientPaint,
    );

    // 添加发光边缘效果
    final Paint edgePaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..color = gradientColors[1].withOpacity(0.7)
      ..isAntiAlias = true;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      0,
      pi / scanAngle,
      false, // 不使用中心点
      edgePaint,
    );
  }

  @override
  bool shouldRepaint(covariant ScanLinePainter oldDelegate) {
    return oldDelegate.scanAngle != scanAngle ||
        oldDelegate.gradientColors != gradientColors;
  }
}
