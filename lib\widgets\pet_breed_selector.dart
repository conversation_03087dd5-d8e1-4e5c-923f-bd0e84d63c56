import 'package:flutter/material.dart';
import '../models/pet.dart';
import '../constants/pet_breeds.dart';
import 'flat_input_field.dart';

/// 宠物品种选择器组件
class PetBreedSelector extends StatelessWidget {
  final PetSpecies selectedSpecies;
  final String? selectedBreed;
  final bool isCustomBreed;
  final TextEditingController customBreedController;
  final Function(String?) onBreedChanged;
  final Function(bool) onCustomBreedChanged;

  const PetBreedSelector({
    Key? key,
    required this.selectedSpecies,
    required this.selectedBreed,
    required this.isCustomBreed,
    required this.customBreedController,
    required this.onBreedChanged,
    required this.onCustomBreedChanged,
  }) : super(key: key);

  String _getDisplayBreed() {
    if (selectedBreed == null) {
      return '请选择品种';
    }
    return isCustomBreed ? customBreedController.text : selectedBreed!;
  }

  void _showBreedPicker(BuildContext context) {
    final breeds = PetBreeds.getBreedsForSpecies(selectedSpecies.value);
    
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.6,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '选择品种',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Navigator.pop(context),
                    child: Icon(
                      Icons.close,
                      color: Colors.grey.shade600,
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView.separated(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                itemCount: breeds.length,
                separatorBuilder: (context, index) => Divider(
                  color: Colors.grey.shade300,
                  height: 1,
                ),
                itemBuilder: (context, index) {
                  final breed = breeds[index];
                  final isSelected = selectedBreed == breed;
                  final isCustomOption = breed == '其他';
                  
                  return ListTile(
                    contentPadding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                    leading: Icon(
                      isCustomOption ? Icons.edit : Icons.pets,
                      color: isSelected ? Colors.purple.shade400 : Colors.grey.shade500,
                      size: 20,
                    ),
                    title: Text(
                      breed,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                        color: isSelected ? Colors.purple.shade700 : Colors.grey.shade700,
                      ),
                    ),
                    trailing: isSelected
                        ? Icon(
                            Icons.check_circle,
                            color: Colors.purple.shade400,
                            size: 20,
                          )
                        : null,
                    onTap: () {
                      onBreedChanged(breed);
                      onCustomBreedChanged(isCustomOption);
                      Navigator.pop(context);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 4, bottom: 12),
            child: Row(
              children: [
                Icon(
                  Icons.category,
                  color: Colors.purple.shade400,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '品种',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () => _showBreedPicker(context),
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.pets,
                    color: Colors.purple.shade400,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      _getDisplayBreed(),
                      style: TextStyle(
                        fontSize: 16,
                        color: selectedBreed != null ? Colors.black87 : Colors.grey.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.grey.shade600,
                    size: 24,
                  ),
                ],
              ),
            ),
          ),
          if (isCustomBreed) ...[
            const SizedBox(height: 16),
            FlatInputField(
              controller: customBreedController,
              label: '自定义品种',
              hint: PetBreeds.getCustomBreedHint(selectedSpecies.value),
              icon: Icons.edit,
              iconColor: Colors.green.shade600,
              validator: (value) {
                if (isCustomBreed && (value == null || value.trim().isEmpty)) {
                  return '请输入品种名称';
                }
                return null;
              },
            ),
          ],
        ],
      ),
    );
  }
}
