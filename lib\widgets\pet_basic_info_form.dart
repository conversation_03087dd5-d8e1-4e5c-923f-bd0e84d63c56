import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'flat_input_field.dart';
import 'flat_date_field.dart';

/// 宠物基本信息表单组件
class PetBasicInfoForm extends StatelessWidget {
  final TextEditingController nicknameController;
  final TextEditingController shoulderHeightController;
  final TextEditingController weightController;
  final DateTime selectedBirthday;
  final VoidCallback onBirthdayTap;

  const PetBasicInfoForm({
    Key? key,
    required this.nicknameController,
    required this.shoulderHeightController,
    required this.weightController,
    required this.selectedBirthday,
    required this.onBirthdayTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 4, bottom: 12),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: Colors.blue.shade400,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '基本信息',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
              ],
            ),
          ),
          FlatInputField(
            controller: nicknameController,
            label: '昵称',
            hint: '给您的爱宠起个可爱的名字',
            icon: Icons.favorite,
            iconColor: Colors.red.shade400,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '请输入宠物昵称';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          FlatDateField(
            label: '生日',
            selectedDate: selectedBirthday,
            icon: Icons.cake,
            iconColor: Colors.pink.shade400,
            onTap: onBirthdayTap,
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: FlatInputField(
                  controller: shoulderHeightController,
                  label: '肩高 (cm)',
                  hint: '0.0',
                  icon: Icons.height,
                  iconColor: Colors.green.shade600,
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入肩高';
                    }
                    final height = double.tryParse(value);
                    if (height == null || height <= 0) {
                      return '请输入有效的肩高';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: FlatInputField(
                  controller: weightController,
                  label: '体重 (kg)',
                  hint: '0.0',
                  icon: Icons.monitor_weight,
                  iconColor: Colors.orange.shade600,
                  keyboardType: const TextInputType.numberWithOptions(decimal: true),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入体重';
                    }
                    final weight = double.tryParse(value);
                    if (weight == null || weight <= 0) {
                      return '请输入有效的体重';
                    }
                    return null;
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
