import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:vibration/vibration.dart';

class QRScanner extends StatefulWidget {
  final Function(String) onDetect;

  const QRScanner({Key? key, required this.onDetect}) : super(key: key);

  @override
  State<QRScanner> createState() => _QRScannerState();
}

class _QRScannerState extends State<QRScanner>
    with SingleTickerProviderStateMixin {
  late MobileScannerController _controller;
  late AnimationController _animationController;
  late Animation<double> _animation;
  double _zoomFactor = 0.0; // mobile_scanner的缩放范围是0.0-1.0
  bool _isFlashOn = false;
  bool _isDetecting = false;

  @override
  void initState() {
    super.initState();
    _controller = MobileScannerController(
      detectionSpeed: DetectionSpeed.noDuplicates,
      detectionTimeoutMs: 500,
      facing: CameraFacing.back,
      cameraResolution: const Size(1920, 1080), // 高分辨率提高识别精度
      autoStart: true,
      returnImage: false,
      // 注意：autoZoom 在 6.0.10 版本中可能不可用，我们稍后会添加手动缩放
    );

    // 初始化扫描线动画
    _animationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_animationController);

    // 循环播放动画
    _animationController.repeat();

    // 设置扫描窗口（延迟执行以确保控制器已初始化）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setScanWindow();
    });
  }

  void _setScanWindow() {
    final screenSize = MediaQuery.of(context).size;
    final scanAreaWidth = screenSize.width * 0.9;
    final scanAreaHeight = scanAreaWidth * 1.2;
    final scanAreaTop = (screenSize.height - scanAreaHeight) / 2 - 100;
    final scanAreaLeft = (screenSize.width - scanAreaWidth) / 2;

    final scanWindow = Rect.fromLTWH(
      scanAreaLeft / screenSize.width,
      scanAreaTop / screenSize.height,
      scanAreaWidth / screenSize.width,
      scanAreaHeight / screenSize.height,
    );

    _controller.updateScanWindow(scanWindow);
  }

  bool _isControllerReady() {
    try {
      return _controller.value.isRunning;
    } catch (e) {
      print('Controller not ready: $e');
      return false;
    }
  }

  void _showControllerNotReadyMessage() {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('相机正在启动中，请稍后再试'),
          duration: Duration(seconds: 1),
        ),
      );
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _zoomIn() {
    print('ZoomIn called: current _zoomFactor = $_zoomFactor');
    if (!_isControllerReady()) {
      print('Controller not ready, skipping zoom');
      _showControllerNotReadyMessage();
      return;
    }

    if (_zoomFactor < 1.0) {
      final newZoom = (_zoomFactor + 0.1).clamp(0.0, 1.0); // 正确的范围0.0-1.0
      setState(() {
        _zoomFactor = newZoom;
      });
      print('ZoomIn: setting zoom to $newZoom');
      try {
        _controller.setZoomScale(_zoomFactor);
      } catch (e) {
        print('Error setting zoom: $e');
      }
    }
  }

  void _zoomOut() {
    print('ZoomOut called: current _zoomFactor = $_zoomFactor');
    if (!_isControllerReady()) {
      print('Controller not ready, skipping zoom');
      _showControllerNotReadyMessage();
      return;
    }

    if (_zoomFactor > 0.0) {
      final newZoom = (_zoomFactor - 0.02).clamp(0.0, 1.0); // 正确的范围0.0-1.0
      setState(() {
        _zoomFactor = newZoom;
      });
      print('ZoomOut: setting zoom to $newZoom');
      try {
        _controller.setZoomScale(_zoomFactor);
      } catch (e) {
        print('Error setting zoom: $e');
      }
    }
  }

  void _resetZoom() {
    print('Reset zoom called');
    if (!_isControllerReady()) {
      print('Controller not ready, skipping reset');
      _showControllerNotReadyMessage();
      return;
    }

    setState(() {
      _zoomFactor = 0.0; // 重置到最小缩放
    });

    try {
      _controller.resetZoomScale();
      print('Zoom reset to: $_zoomFactor');
    } catch (e) {
      print('Error resetting zoom: $e');
    }
  }

  void _toggleFlash() {
    setState(() {
      _isFlashOn = !_isFlashOn;
    });
    _controller.toggleTorch();
  }



  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final scanAreaWidth = screenSize.width * 0.9;
    final scanAreaHeight = scanAreaWidth * 1.2; // 拉长扫描框
    final scanAreaTop = (screenSize.height - scanAreaHeight) / 2 - 100; // 上移更多
    final scanAreaBottom = scanAreaTop + scanAreaHeight;

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // 相机预览
          MobileScanner(
            controller: _controller,
            onDetect: (capture) {
              setState(() {
                _isDetecting = true;
              });

              final List<Barcode> barcodes = capture.barcodes;

              if (barcodes.isNotEmpty) {
                final String? code = barcodes.first.rawValue;
                if (code != null) {
                  // 添加震动反馈
                  Vibration.vibrate(duration: 200);
                  widget.onDetect(code);
                }
              }

              // 重置检测状态
              Future.delayed(const Duration(milliseconds: 500), () {
                if (mounted) {
                  setState(() {
                    _isDetecting = false;
                  });
                }
              });
            },
          ),

          // 遮罩层，创建扫描窗口效果
          Stack(
            children: [
              // 上方遮罩
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                height: scanAreaTop,
                child: Container(color: Colors.black.withOpacity(0.8)),
              ),
              // 下方遮罩（收缩的黑边）
              Positioned(
                top: scanAreaBottom,
                left: 0,
                right: 0,
                bottom: 0,
                child: Container(color: Colors.black.withOpacity(0.8)),
              ),
              // 左侧遮罩
              Positioned(
                top: scanAreaTop,
                left: 0,
                width: (screenSize.width - scanAreaWidth) / 2,
                height: scanAreaHeight,
                child: Container(color: Colors.black.withOpacity(0.8)),
              ),
              // 右侧遮罩
              Positioned(
                top: scanAreaTop,
                right: 0,
                width: (screenSize.width - scanAreaWidth) / 2,
                height: scanAreaHeight,
                child: Container(color: Colors.black.withOpacity(0.8)),
              ),
            ],
          ),

          // 扫描框
          Positioned(
            top: scanAreaTop,
            left: (screenSize.width - scanAreaWidth) / 2,
            child: Container(
              width: scanAreaWidth,
              height: scanAreaHeight,
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.white,
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Stack(
                children: [
                  // 四个角的装饰
                  ...List.generate(4, (index) {
                    final isTop = index < 2;
                    final isLeft = index % 2 == 0;
                    final cornerColor = _isDetecting ? Colors.orange : Colors.green;
                    return Positioned(
                      top: isTop ? 8 : null,
                      bottom: isTop ? null : 8,
                      left: isLeft ? 8 : null,
                      right: isLeft ? null : 8,
                      child: Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          border: Border(
                            top: isTop
                                ? BorderSide(color: cornerColor, width: 3)
                                : BorderSide.none,
                            bottom: !isTop
                                ? BorderSide(color: cornerColor, width: 3)
                                : BorderSide.none,
                            left: isLeft
                                ? BorderSide(color: cornerColor, width: 3)
                                : BorderSide.none,
                            right: !isLeft
                                ? BorderSide(color: cornerColor, width: 3)
                                : BorderSide.none,
                          ),
                        ),
                      ),
                    );
                  }),

                  // 检测状态指示器
                  if (_isDetecting)
                    Positioned(
                      top: 10,
                      right: 10,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.orange.withOpacity(0.8),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          '检测中...',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),

                  // 扫描线动画
                  AnimatedBuilder(
                    animation: _animation,
                    builder: (context, child) {
                      return Positioned(
                        top: _animation.value * (scanAreaHeight - 4),
                        left: 2,
                        right: 2,
                        child: Container(
                          height: 2,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Colors.transparent,
                                Colors.green,
                                Colors.green,
                                Colors.transparent,
                              ],
                              stops: const [0.0, 0.3, 0.7, 1.0],
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.green.withOpacity(0.5),
                                blurRadius: 4,
                                spreadRadius: 1,
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),

          // 提示文字
          Positioned(
            top: scanAreaTop - 80,
            left: 20,
            right: 20,
            child: Column(
              children: [
                const Text(
                  '将二维码放入框内进行扫描',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '小二维码识别困难？试试手动缩放或开启手电筒',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Colors.white.withOpacity(0.8),
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),

          // 缩放控制按钮
          Positioned(
            bottom: 140,
            left: 20,
            right: 20,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.black.withOpacity(0.6),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 缩小按钮
                  Container(
                    decoration: BoxDecoration(
                      color: _zoomFactor > 0.0
                          ? Colors.green.withOpacity(0.8)
                          : Colors.grey.withOpacity(0.5),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: _zoomFactor > 0.0 ? _zoomOut : null,
                      icon: const Icon(
                        Icons.remove,
                        color: Colors.white,
                        size: 20,
                      ),
                      padding: const EdgeInsets.all(8),
                      constraints: const BoxConstraints(
                        minWidth: 40,
                        minHeight: 40,
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // 缩放显示
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${(1.0 + _zoomFactor * 1.5).toStringAsFixed(2)}x', // 转换为1.0-2.5x显示
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // 放大按钮
                  Container(
                    decoration: BoxDecoration(
                      color: _zoomFactor < 1.0
                          ? Colors.green.withOpacity(0.8)
                          : Colors.grey.withOpacity(0.5),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: _zoomFactor < 1.0 ? _zoomIn : null,
                      icon: const Icon(
                        Icons.add,
                        color: Colors.white,
                        size: 20,
                      ),
                      padding: const EdgeInsets.all(8),
                      constraints: const BoxConstraints(
                        minWidth: 40,
                        minHeight: 40,
                      ),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // 重置按钮
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.orange.withOpacity(0.8),
                      shape: BoxShape.circle,
                    ),
                    child: IconButton(
                      onPressed: _resetZoom,
                      icon: const Icon(
                        Icons.refresh,
                        color: Colors.white,
                        size: 18,
                      ),
                      padding: const EdgeInsets.all(6),
                      constraints: const BoxConstraints(
                        minWidth: 36,
                        minHeight: 36,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 控制按钮（位于收缩的黑边中）
          Positioned(
            bottom: 60,
            left: 0,
            right: 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 手电筒按钮
                Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: IconButton(
                    color: _isFlashOn ? Colors.yellow : Colors.white,
                    icon: Icon(
                      _isFlashOn ? Icons.flash_on : Icons.flash_off,
                      size: 28,
                    ),
                    onPressed: _toggleFlash,
                  ),
                ),
                const SizedBox(width: 40),

                // 切换摄像头按钮
                Container(
                  decoration: BoxDecoration(
                    color: Colors.black.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: IconButton(
                    color: Colors.white,
                    icon: const Icon(Icons.flip_camera_ios, size: 28),
                    onPressed: () => _controller.switchCamera(),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
