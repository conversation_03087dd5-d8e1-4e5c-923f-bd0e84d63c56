import 'package:flutter/material.dart';

import 'package:jwt_decoder/jwt_decoder.dart';
import '../services/auth_service.dart';
import '../models/user.dart';
import '../utils/user_context.dart';
import '../utils/app_logger.dart';

class AuthProvider with ChangeNotifier {
  final AuthService _authService;
  AuthProvider(this._authService);

  User? _user;
  User? get user => _user;
  bool get isLogin => _user?.isLogin ?? false;

  Future<void> init() async {
    _user = await User.getUser();
    if (_user != null) {
      updateUser({
        'isLogin': false,
      });
      final isExpired = JwtDecoder.isExpired(_user!.refreshToken);
      if (!isExpired) {
        await refreshToken();
      }
    }
  }

  Future<void> updateUser(Map<String, dynamic> data) async {
    if (_user == null) {
      throw Exception('用户不存在');
    }
    Map<String, dynamic> merged = {..._user!.toJson(), ...data};
    _user = User.fromJson(merged);
    await User.saveUser(_user!);
    notifyListeners();
  }

  Future<void> refreshToken() async {
    final data =
        await _authService.refreshToken(_user!.phone, _user!.refreshToken);
    updateUser(data);
  }

  Future<void> getSmsCode(String phone) async {
    try {
      await _authService.getSmsCode(phone);
    } catch (e) {
      rethrow;
    }
  }

  Future<void> login(String phone, String smsCode) async {
    try {
      final data = await _authService.login(phone, smsCode);
      data['phone'] = phone;
      _user = User.fromJson(data);
      await User.saveUser(_user!);

      // 设置用户上下文并迁移现有数据
      try {
        UserContext.instance.setCurrentUser(phone);
        await UserContext.instance.migrateExistingDataToCurrentUser();
      } catch (e) {
        // 迁移失败不应该阻止登录，只记录错误
        AppLogger.error('数据迁移失败: $e', error: e);
      }

      notifyListeners();
    } catch (e) {
      rethrow;
    }
  }

  Future<void> logout() async {
    try {
      await clearUser();
      notifyListeners();
      await _authService.logout();
    } catch (e) {
      await _authService.logout();
    }
  }

  Future<void> clearUser() async {
    if (_user != null) {
      // 清理用户相关的本地数据
      try {
        await UserContext.instance.clearCurrentUserData();
      } catch (e) {
        // 清理失败不应该阻止用户退出登录，只记录错误
        AppLogger.error('清理用户数据失败: $e', error: e);
      }

      // 清除用户上下文
      UserContext.instance.setCurrentUser(null);

      _user = null;
      await User.clearUser();
      notifyListeners();
    }
  }
}
