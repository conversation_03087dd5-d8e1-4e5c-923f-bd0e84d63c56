// lib/services/health_service.dart
import '../services/api_service.dart';
import '../utils/toast_utils.dart';
import '../constants/url_form.dart';

/// HealthService 负责与后端API进行交互，管理健康数据的获取、添加、更新和删除。
class HealthService {
  final ApiService _apiService;

  /// 构造函数，注入 ApiService
  HealthService(this._apiService);

  /// 获取指定用户的所有健康数据
  Future<Map<String, dynamic>> fetchHealthData(String deviceName) async {
    final body = {
      'device_name': deviceName,
    };
    final jsonResponse = await _apiService.postRequest(
      UrlFormat.SensorlatestData,
      body,
    );
    final data = jsonResponse['data'];
    final code = jsonResponse['code'];
    if (code == 200) {
      return data;
    } else {
      ToastUtils.showError('获取健康数据失败');
      throw Exception('获取健康数据失败');
    }
  }
}
