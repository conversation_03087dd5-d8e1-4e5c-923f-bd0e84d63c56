import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';
import '../utils/l10n.dart';
import '../constants/constants.dart';
import '../utils/app_logger.dart';
import '../utils/toast_utils.dart';

class LoginScreen extends StatefulWidget {
  @override
  _LoginScreenState createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  String _phone = '';
  String _smsCode = '';
  bool _isLoading = false;
  bool _isGettingCode = false;
  int _countDown = 60;
  Timer? _timer;
  bool? _agreedToTerms = false;

  TextEditingController _phoneController = TextEditingController();
  TextEditingController _smsCodeController = TextEditingController();

  void _login() async {
    if (_agreedToTerms != true) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请先同意用户协议和隐私政策')),
      );
      return;
    }

    if (!_formKey.currentState!.validate()) return;
    _formKey.currentState!.save();

    setState(() {
      _isLoading = true;
    });

    try {
      await Provider.of<AuthProvider>(context, listen: false)
          .login(_phone, _smsCode);
      Navigator.of(context).pushReplacementNamed(Routes.home);
    } catch (e, stackTrace) {
      AppLogger.error('登录失败：$e', error: e, stackTrace: stackTrace);
      ToastUtils.showError(e.toString());
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // 获取手机验证码
  void _getVerificationCode() async {
    if (_phoneController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请先输入手机号')),
      );
      return;
    }

    if (!RegExp(r'^[0-9]{11}$').hasMatch(_phoneController.text)) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请输入有效的手机号')),
      );
      return;
    }

    setState(() {
      _isGettingCode = true;
      _countDown = 60;
    });

    try {
      await Provider.of<AuthProvider>(context, listen: false)
          .getSmsCode(_phoneController.text);
      _timer = Timer.periodic(Duration(seconds: 1), (timer) {
        setState(() {
          if (_countDown > 0) {
            _countDown--;
          } else {
            _isGettingCode = false;
            _timer?.cancel();
          }
        });
      });
    } catch (e, stackTrace) {
      AppLogger.error('获取验证码失败：$e', error: e, stackTrace: stackTrace);
      ToastUtils.showError(e.toString());
      setState(() {
        _isGettingCode = false;
      });
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    L10n.init(context); // 初始化本地化?

    return Scaffold(
      appBar: AppBar(
        title: Text('登录'),
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Padding(
              padding:
                  const EdgeInsets.only(left: 16.0, right: 16.0, top: 50.0),
              child: Column(
                children: [
                  Form(
                    key: _formKey,
                    child: Column(
                      children: [
                        TextFormField(
                          controller: _phoneController,
                          decoration: InputDecoration(
                            labelText: '手机号',
                            hintText: '请输入手机号码',
                            hintStyle: TextStyle(color: Colors.grey[350]),
                            prefixIcon: Icon(Icons.smartphone),
                          ),
                          keyboardType: TextInputType.phone,
                          validator: (value) {
                            if (value == null || value.isEmpty) return '请输入手机号';
                            if (!RegExp(r'^[0-9]{11}$').hasMatch(value))
                              return '请输入有效的手机号';
                            return null;
                          },
                          onSaved: (value) => _phone = value!.trim(),
                        ),
                        SizedBox(height: 20),
                        TextFormField(
                          controller: _smsCodeController,
                          decoration: InputDecoration(
                            labelText: '验证码',
                            hintText: '请输入短信验证码',
                            hintStyle: TextStyle(color: Colors.grey[350]),
                            prefixIcon: Icon(Icons.sms),
                            suffixIcon: TextButton(
                              onPressed:
                                  _isGettingCode ? null : _getVerificationCode,
                              child: Text(
                                _isGettingCode
                                    ? '${_countDown}s后重新获取'
                                    : '获取验证码',
                                style: TextStyle(
                                  color: _isGettingCode
                                      ? Colors.grey
                                      : Colors.orangeAccent,
                                ),
                              ),
                            ),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) return '请输入验证码';
                            if (value.length < 6) return '验证码长度至少为6位';
                            return null;
                          },
                          onSaved: (value) => _smsCode = value!.trim(),
                        ),
                        SizedBox(height: 20),
                        SizedBox(
                          width: double.infinity,
                          height: 48, // 设置一个合适的按钮高度
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : _login,
                            child: Text('登录'),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 20),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Checkbox(
                        value: _agreedToTerms ?? false,
                        onChanged: (bool? value) {
                          setState(() {
                            _agreedToTerms = value;
                          });
                        },
                      ),
                      Expanded(
                        child: Text.rich(
                          TextSpan(
                            text: '我已阅读并同意',
                            style: TextStyle(fontSize: 12),
                            children: [
                              TextSpan(
                                text: '《用户服务协议》',
                                style: TextStyle(color: Colors.blue),
                                // 跳转到用户协议页面
                              ),
                              TextSpan(text: '及'),
                              TextSpan(
                                text: '《隐私协议》',
                                style: TextStyle(color: Colors.blue),
                                // 跳转到隐私协议页面
                              ),
                              TextSpan(text: ', 未注册用户登录后将自动注册账号'),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          if (_isLoading)
            Container(
              color: Colors.black.withOpacity(0.3),
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),
        ],
      ),
    );
  }
}
