import 'package:flutter/material.dart';

/// 无宠物信息卡片组件
class NoPetCard extends StatelessWidget {
  final VoidCallback? onTap;

  const NoPetCard({
    Key? key,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.only(left: 12, right: 12, top: 4, bottom: 4),
      child: Ink<PERSON>ell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.orange.shade50,
                Colors.orange.shade100,
              ],
            ),
          ),
          child: SizedBox(
            height: 110,
            width: 400,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.edit,
                  size: 40,
                  color: Colors.orange.shade300,
                ),
                const SizedBox(height: 12),
                Text(
                  '小主快来添加您的爱宠信息',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey[600],
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 8),
                Text(
                  '点击添加',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.orange.shade300,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
