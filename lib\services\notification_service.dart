import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/material.dart';
// import 'package:flutter_app_badger/flutter_app_badger.dart';

import '../utils/app_logger.dart';

// 声明通知点击回调类型
typedef NotificationTapCallback = void Function(String? payload);

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final FlutterLocalNotificationsPlugin _flutterLocalNotificationsPlugin =
      FlutterLocalNotificationsPlugin();

  static const String _channelId = 'pet_lost_channel';
  static const String _channelName = '宠物走失警报';
  static const String _channelDescription = '当宠物设备离线时发送警报通知';

  // 通知点击回调函数
  NotificationTapCallback? _onNotificationTap;

  // 设置通知点击回调
  void setOnNotificationTap(NotificationTapCallback callback) {
    _onNotificationTap = callback;
  }

  // 初始化通知服务
  Future<void> init() async {
    try {
      // 初始化安卓设置
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      // 初始化iOS设置
      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestSoundPermission: true,
        requestBadgePermission: true,
        requestAlertPermission: true,
      );

      // 整合所有平台设置
      const InitializationSettings initializationSettings =
          InitializationSettings(
              android: initializationSettingsAndroid,
              iOS: initializationSettingsIOS);

      // 初始化通知插件
      await _flutterLocalNotificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // 创建通知渠道（仅安卓需要）
      await _createNotificationChannel();
    } catch (e, stackTrace) {
      AppLogger.error('初始化通知服务失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  // 创建通知渠道
  Future<void> _createNotificationChannel() async {
    try {
      const AndroidNotificationChannel channel = AndroidNotificationChannel(
        _channelId,
        _channelName,
        description: _channelDescription,
        importance: Importance.max,
        playSound: true,
        enableVibration: true,
        showBadge: true,
      );

      await _flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.createNotificationChannel(channel);
    } catch (e, stackTrace) {
      AppLogger.error('创建通知渠道失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  // 显示宠物走失通知
  Future<void> showPetLostNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    try {
      // 锁屏通知和状态栏通知设置
      const AndroidNotificationDetails androidNotificationDetails =
          AndroidNotificationDetails(
        _channelId,
        _channelName,
        channelDescription: _channelDescription,
        importance: Importance.max,
        priority: Priority.high,
        visibility: NotificationVisibility.public, // 在锁屏上显示完整通知
        fullScreenIntent: true, // 锁屏时全屏通知
        category: AndroidNotificationCategory.alarm, // 设置为警报类别
        ticker: '宠物走失警报',
        color: Colors.red,
        colorized: true,
        ongoing: true, // 持久通知，用户无法轻易清除
        autoCancel: false, // 防止用户点击时自动取消
      );

      const DarwinNotificationDetails iOSNotificationDetails =
          DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        interruptionLevel: InterruptionLevel.critical, // iOS 15+的关键警报
        categoryIdentifier: 'PET_LOST_CATEGORY',
      );

      const NotificationDetails notificationDetails = NotificationDetails(
        android: androidNotificationDetails,
        iOS: iOSNotificationDetails,
      );

      await _flutterLocalNotificationsPlugin.show(
        id,
        title,
        body,
        notificationDetails,
        payload: payload,
      );

      // 设置应用角标
      // try {
      //   await FlutterAppBadger.updateBadgeCount(1);
      // } catch (e) {
      //   // 忽略可能的应用角标操作失败
      // }
    } catch (e, stackTrace) {
      AppLogger.error('显示通知失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  // 取消特定通知
  Future<void> cancelNotification(int id) async {
    try {
      await _flutterLocalNotificationsPlugin.cancel(id);
      // 清除应用角标
      // try {
      //   await FlutterAppBadger.removeBadge();
      // } catch (e) {
      //   // 忽略可能的应用角标操作失败
      // }
    } catch (e, stackTrace) {
      AppLogger.error('取消通知失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  // 取消所有通知
  Future<void> cancelAllNotifications() async {
    try {
      await _flutterLocalNotificationsPlugin.cancelAll();
      // 清除应用角标
      // try {
      //   await FlutterAppBadger.removeBadge();
      // } catch (e) {
      //   // 忽略可能的应用角标操作失败
      // }
    } catch (e, stackTrace) {
      AppLogger.error('取消所有通知失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  // 处理通知点击事件
  void _onNotificationTapped(NotificationResponse response) {
    try {
      // 调用注册的回调
      if (_onNotificationTap != null) {
        _onNotificationTap!(response.payload);
      }
    } catch (e, stackTrace) {
      AppLogger.error('处理通知点击事件失败：$e', error: e, stackTrace: stackTrace);
    }
  }
}
