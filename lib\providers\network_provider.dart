import 'package:flutter/material.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../utils/app_logger.dart';
import 'dart:async';

// NetworkProvider 负责管理网络连接状态的监控和通知。
class NetworkProvider extends ChangeNotifier {
  final Connectivity _connectivity;
  late List<ConnectivityResult> _connectionStatus;
  StreamSubscription<List<ConnectivityResult>>? _connectivitySubscription;

  List<ConnectivityResult> get connectionStatus => _connectionStatus;

  NetworkProvider(this._connectivity) {
    _init();
  }

  void _init() {
    // 监听网络连接状态的变化
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    // 初始化网络连接状态
    _initConnectivity();
  }

  // 初始化网络连接状态
  Future<void> _initConnectivity() async {
    try {
      _connectionStatus = await _connectivity.checkConnectivity();
      notifyListeners();
    } catch (e, stackTrace) {
      AppLogger.error('初始化网络连接状态时发生错误：$e', error: e, stackTrace: stackTrace);
    }
  }

  // 更新网络连接状态
  void _updateConnectionStatus(List<ConnectivityResult> result) {
    try {
      _connectionStatus = result;
      notifyListeners();
    } catch (e, stackTrace) {
      AppLogger.error('更新网络连接状态时发生错误：$e', error: e, stackTrace: stackTrace);
    }
  }

  @override
  void dispose() {
    _connectivitySubscription?.cancel();
    super.dispose();
  }
}
