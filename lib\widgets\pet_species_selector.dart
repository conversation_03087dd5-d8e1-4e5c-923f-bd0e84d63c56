import 'package:flutter/material.dart';
import '../models/pet.dart';

/// 宠物种类选择器组件
class PetSpeciesSelector extends StatelessWidget {
  final PetSpecies selectedSpecies;
  final Function(PetSpecies) onSpeciesChanged;

  const PetSpeciesSelector({
    Key? key,
    required this.selectedSpecies,
    required this.onSpeciesChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 4, bottom: 12),
            child: Row(
              children: [
                Icon(
                  Icons.pets,
                  color: Colors.orange.shade400,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  '宠物种类',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.grey.shade800,
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: PetSpecies.values.map((species) {
              final isSelected = selectedSpecies == species;
              return Expanded(
                child: GestureDetector(
                  onTap: () => onSpeciesChanged(species),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    margin: const EdgeInsets.symmetric(horizontal: 20),
                    decoration: BoxDecoration(
                      color: isSelected
                          ? Colors.orange.shade50
                          : Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected
                            ? Colors.orange.shade300
                            : Colors.grey.shade300,
                        width: 2,
                      ),
                    ),
                    child: Center(
                      child: Text(
                        species.displayName,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: isSelected
                              ? Colors.orange.shade700
                              : Colors.grey.shade600,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
