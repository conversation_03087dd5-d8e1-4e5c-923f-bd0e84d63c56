import 'package:flutter/material.dart';
import '../models/pet.dart';

/// 宠物头部图片组件
class PetHeaderImage extends StatelessWidget {
  final PetSpecies selectedSpecies;

  const PetHeaderImage({
    Key? key,
    required this.selectedSpecies,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 220,
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.orange.shade300, Colors.orange.shade100],
        ),
      ),
      child: Center(
        child: Container(
          width: 160,
          height: 160,
          decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.9),
            borderRadius: BorderRadius.circular(80),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(80),
            child: Image.asset(
              selectedSpecies == PetSpecies.dog
                  ? 'assets/images/pet_dog.png'
                  : 'assets/images/pet_cat.png',
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) {
                return Icon(
                  Icons.pets,
                  size: 80,
                  color: Colors.orange.shade600,
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}
