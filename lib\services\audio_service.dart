import 'package:audioplayers/audioplayers.dart';

import '../utils/app_logger.dart';

class AudioService {
  final AudioPlayer _audioPlayer = AudioPlayer();
  bool _isPlaying = false;

  // 播放报警音频
  Future<void> playAlarm() async {
    try {
      if (!_isPlaying) {
        _isPlaying = true;

        // 播放报警音效
        await _audioPlayer.play(AssetSource('audios/alert.wav'));

        // 设置循环播放
        await _audioPlayer.setReleaseMode(ReleaseMode.loop);

        // 设置音量
        await _audioPlayer.setVolume(1.0);
      }
    } catch (e, stackTrace) {
      _isPlaying = false;
      AppLogger.error('播放报警音频失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  // 停止播放
  Future<void> stopAlarm() async {
    try {
      if (_isPlaying) {
        await _audioPlayer.stop();
        _isPlaying = false;
      }
    } catch (e, stackTrace) {
      AppLogger.error('停止播放音频失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  // 释放资源
  Future<void> dispose() async {
    try {
      await _audioPlayer.dispose();
    } catch (e, stackTrace) {
      AppLogger.error('释放音频资源失败：$e', error: e, stackTrace: stackTrace);
    }
  }
}
