// lib/utils/app_logger.dart
import 'package:logger/logger.dart'; // 核心日志库
import 'package:stack_trace/stack_trace.dart' as st; // 用于解析堆栈跟踪的辅助库

class AppLogger {
  // 1. 私有静态 Logger 实例
  static final Logger _logger = Logger(
    // 2. 配置 Printer (格式化器)
    printer: PrettyPrinter(
      methodCount: 1, // 调用栈中显示的方法数量 (Logger 内部调用点)
      errorMethodCount: 8, // 错误发生时显示的调用栈方法数量
      lineLength: 120, // 每行字符数，用于格式化输出
      colors: true, // 是否在控制台输出中使用颜色区分日志级别
      printEmojis: true, // 是否在日志级别前打印 Emoji 图标
      dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart, // 是否打印日志记录的时间戳
    ),
    // 3. 可选: 配置 LogFilter (过滤器)
    // filter: ProductionFilter(), // 例如，在生产环境中只输出警告及以上级别的日志

    // 4. 可选: 配置 LogOutput (输出目的地)
    // output: MultiOutput([ConsoleOutput(), FileOutput()]), // 例如，同时输出到控制台和文件
  );

  // 5. 辅助方法: _getSourceInfo - 获取更精确的日志调用源信息
  static String _getSourceInfo(StackTrace? stackTrace, {int framesToSkip = 2}) {
    try {
      // 使用传入的 stackTrace，如果为 null (例如非错误日志)，则使用 StackTrace.current
      final trace = st.Trace.from(stackTrace ?? StackTrace.current);

      // 检查是否有足够的帧可以跳过
      if (trace.frames.length > framesToSkip) {
        // 跳过指定数量的帧来定位到 AppLogger 方法的调用者
        // framesToSkip = 0: _getSourceInfo 本身
        // framesToSkip = 1: AppLogger 的公共方法 (如 AppLogger.info)
        // framesToSkip = 2: AppLogger.info 的实际调用者 (我们想要这个)
        final frame = trace.frames[framesToSkip];

        // 从 URI 中提取文件名
        final path = frame.uri.pathSegments.isNotEmpty
            ? frame.uri.pathSegments.last
            : 'unknown_file';
        // 提取方法名，并清理 (例如 <fn> 变为 anonymous_closure)
        String member = frame.member?.split('.').last ?? 'unknown_method';
        if (member.startsWith('<') && member.endsWith('>')) {
          member = 'anonymous_closure';
        }
        // 返回格式: "filename.dart (methodName:lineNumber)"
        return '$path ($member:${frame.line})';
      }
      // 如果堆栈帧不足，返回一个默认值
      return 'unknown_source (short_stack)';
    } catch (e) {
      // 如果解析堆栈跟踪时发生错误，返回一个错误标识
      // 在生产中，你可能不想在这里打印错误，而是返回一个静默的标识符
      // print("Error parsing stack trace for source: $e");
      return 'source_parse_error';
    }
  }

  // 6. 日志级别方法 (verbose, debug, info, warning, error, fatal)
  //    每个方法都遵循类似的模式

  // 示例: info 方法
  static void info(String message, {String? moduleTag, Object? data}) {
    // a. 获取源信息: 使用 StackTrace.current 因为 info 通常不与捕获的异常关联
    final source = _getSourceInfo(StackTrace.current); // framesToSkip 默认为 2

    // b. 构建日志消息:
    //    - [moduleTag ?? source]: 如果提供了 moduleTag，使用它；否则使用解析出的源文件/行号。
    //    - message: 主要的日志内容。
    //    - data: 可选的附加数据对象，会打印其 toString() 表示。
    _logger.i(// i 代表 info 级别
        "[${moduleTag ?? source}] $message ${data != null ? '\nData: $data' : ''}");
  }

  // 示例: error 方法
  static void error(
    String message, {
    String? moduleTag,
    required Object error, // 错误对象 (例如 Exception 实例)
    StackTrace? stackTrace, // 与错误关联的堆栈跟踪 (通常来自 try-catch)
  }) {
    // a. 获取源信息: 优先使用传入的 stackTrace (来自 catch块)
    //    如果 stackTrace 为 null，则 _getSourceInfo 内部会使用 StackTrace.current
    final source = _getSourceInfo(stackTrace);

    // b. 调用 _logger.e() (e 代表 error 级别):
    //    - PrettyPrinter 会很好地格式化 error 对象和 stackTrace
    _logger.e("[${moduleTag ?? source}] $message", // 主消息
        error: error, // 传递错误对象
        stackTrace: stackTrace ?? StackTrace.current // 传递堆栈跟踪 (如果为null，则使用当前堆栈)
        );
  }

  // 其他级别的方法 (verbose, debug, warning, fatal) 结构类似:
  // verbose (最详细的日志，用 _logger.t 或 _logger.v)
  static void verbose(String message, {String? moduleTag, Object? data}) {
    final source = _getSourceInfo(StackTrace.current);
    _logger.t(
        "[${moduleTag ?? source}] $message ${data != null ? '\nData: $data' : ''}"); // 't' for trace/verbose
  }

  // debug
  static void debug(String message, {String? moduleTag, Object? data}) {
    final source = _getSourceInfo(StackTrace.current);
    _logger.d(
        "[${moduleTag ?? source}] $message ${data != null ? '\nData: $data' : ''}");
  }

  // warning
  static void warning(String message,
      {String? moduleTag, Object? error, StackTrace? stackTrace}) {
    final source = _getSourceInfo(stackTrace);
    _logger.w("[${moduleTag ?? source}] $message",
        error: error, stackTrace: stackTrace);
  }

  // fatal (比 error 更严重的错误，用 _logger.f 或 _logger.wtf)
  static void fatal(
    String message, {
    String? moduleTag,
    required Object error,
    StackTrace? stackTrace,
  }) {
    final source = _getSourceInfo(stackTrace);
    _logger.f("[${moduleTag ?? source}] $message",
        error: error,
        stackTrace: stackTrace ?? StackTrace.current); // 'f' for fatal
  }
}
