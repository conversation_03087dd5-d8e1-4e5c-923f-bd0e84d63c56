import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../utils/user_context.dart';
import '../constants/activity_status.dart';
import '../constants/emotion_status.dart';
import '../constants/health_detection.dart';

/// 扩展健康数据模型，包含所有健康监测数据
class ExtendedHealthData {
  // 基础数据（保持与原HealthData兼容）
  final int running;
  final int walking;
  final int jumping;
  final int stepCount;
  final double temperature;
  final DateTime datetime;

  // 扩展数据
  final double calories; // 卡路里消耗
  final ActivityStatus activityStatus; // 当前活动状态
  final EmotionStatus emotionStatus; // 当前情绪状态
  final List<HealthDetection> healthDetections; // 健康检测结果集合

  ExtendedHealthData({
    required this.running,
    required this.walking,
    required this.jumping,
    required this.stepCount,
    required this.temperature,
    required this.datetime,
    required this.calories,
    required this.activityStatus,
    required this.emotionStatus,
    required this.healthDetections,
  });

  /// 活动数据映射（保持兼容性）
  Map<String, int> get activity => {
        'running': running,
        'walking': walking,
        'jumping': jumping,
      };

  /// 从 JSON 数据创建 ExtendedHealthData 实例
  factory ExtendedHealthData.fromJson(Map<String, dynamic> json) {
    return ExtendedHealthData(
      running: json['running']?.toInt() ?? 0,
      walking: json['walking']?.toInt() ?? 0,
      jumping: json['jumping']?.toInt() ?? 0,
      stepCount: json['stepCount']?.toInt() ?? 0,
      temperature: json['temperature']?.toDouble() ?? 37.0,
      datetime: _parseTimestamp(json['datetime']),
      calories: json['calories']?.toDouble() ?? 0.0,
      activityStatus: json['activityStatus'] != null
          ? ActivityStatus.fromString(json['activityStatus'])
          : ActivityStatus.stationary,
      emotionStatus: json['emotionStatus'] != null
          ? EmotionStatus.fromString(json['emotionStatus'])
          : EmotionStatus.stable,
      healthDetections: json['healthDetections'] != null
          ? (json['healthDetections'] as List<dynamic>)
              .map((item) => HealthDetection.fromString(item.toString()))
              .toList()
          : [],
    );
  }

  /// 将 ExtendedHealthData 实例转换为 JSON 格式
  Map<String, dynamic> toJson() {
    return {
      'running': running,
      'walking': walking,
      'jumping': jumping,
      'stepCount': stepCount,
      'temperature': temperature,
      'datetime': datetime.toIso8601String(),
      'calories': calories,
      'activityStatus': activityStatus.displayName,
      'emotionStatus': emotionStatus.displayName,
      'healthDetections': healthDetections.map((detection) => detection.displayName).toList(),
    };
  }

  /// 解析时间戳
  static DateTime _parseTimestamp(dynamic timestamp) {
    if (timestamp == null) {
      return DateTime.now();
    }
    
    if (timestamp is String) {
      try {
        return DateTime.parse(timestamp);
      } catch (e) {
        return DateTime.now();
      }
    }
    
    if (timestamp is int) {
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    }
    
    return DateTime.now();
  }

  /// 从本地存储获取扩展健康数据
  static Future<ExtendedHealthData?> getExtendedHealthData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = UserContext.instance.currentUserPhone ?? 'default';
      final key = 'extended_health_data_$userId';
      final jsonString = prefs.getString(key);
      
      if (jsonString != null) {
        final jsonData = json.decode(jsonString);
        return ExtendedHealthData.fromJson(jsonData);
      }
    } catch (e) {
      print('获取扩展健康数据失败: $e');
    }
    return null;
  }

  /// 保存扩展健康数据到本地存储
  static Future<void> saveExtendedHealthData(ExtendedHealthData data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userId = UserContext.instance.currentUserPhone ?? 'default';
      final key = 'extended_health_data_$userId';
      final jsonString = json.encode(data.toJson());
      await prefs.setString(key, jsonString);
    } catch (e) {
      print('保存扩展健康数据失败: $e');
    }
  }

  /// 检查是否有高危健康检测结果
  bool get hasHighRiskDetections {
    return healthDetections.any((detection) => detection.isHighRisk);
  }

  /// 检查是否有发热检测结果
  bool get hasFeverDetection {
    return healthDetections.contains(HealthDetection.fever);
  }

  /// 获取健康检测结果的显示文本
  String get healthDetectionDisplayText {
    if (healthDetections.isEmpty) {
      return '正常';
    }
    return healthDetections.map((detection) => detection.displayName).join(', ');
  }

  /// 复制并更新数据
  ExtendedHealthData copyWith({
    int? running,
    int? walking,
    int? jumping,
    int? stepCount,
    double? temperature,
    DateTime? datetime,
    double? calories,
    ActivityStatus? activityStatus,
    EmotionStatus? emotionStatus,
    List<HealthDetection>? healthDetections,
  }) {
    return ExtendedHealthData(
      running: running ?? this.running,
      walking: walking ?? this.walking,
      jumping: jumping ?? this.jumping,
      stepCount: stepCount ?? this.stepCount,
      temperature: temperature ?? this.temperature,
      datetime: datetime ?? this.datetime,
      calories: calories ?? this.calories,
      activityStatus: activityStatus ?? this.activityStatus,
      emotionStatus: emotionStatus ?? this.emotionStatus,
      healthDetections: healthDetections ?? this.healthDetections,
    );
  }
}
