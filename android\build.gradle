allprojects {
    repositories {
        // maven { url 'https://maven.aliyun.com/repository/google' }
        // maven { url 'https://maven.aliyun.com/repository/jcenter' }
        // maven { url 'https://maven.aliyun.com/nexus/content/groups/public' }

        google()
        mavenCentral()
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
