import 'package:flutter/material.dart';
import 'dart:math' as math;

class CustomWaveformWidget extends StatefulWidget {
  final List<double> waveData;
  final Size size;
  final Color waveColor;
  final Color liveWaveColor;
  final Color centerLineColor;
  final bool isRecording;
  final double scaleFactor;

  const CustomWaveformWidget({
    Key? key,
    required this.waveData,
    required this.size,
    this.waveColor = Colors.blue,
    this.liveWaveColor = Colors.red,
    this.centerLineColor = Colors.grey,
    this.isRecording = false,
    this.scaleFactor = 70.0,
  }) : super(key: key);

  @override
  State<CustomWaveformWidget> createState() => _CustomWaveformWidgetState();
}

class _CustomWaveformWidgetState extends State<CustomWaveformWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.size.width,
      height: widget.size.height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12.0),
        color: const Color(0xFFF5F5F5),
      ),
      child: CustomPaint(
        painter: CenterLineWaveformPainter(
          recordedWaveData: widget.waveData,
          waveColor: widget.waveColor,
          liveWaveColor: widget.liveWaveColor,
          centerLineColor: widget.centerLineColor,
          isRecording: widget.isRecording,
          scaleFactor: widget.scaleFactor,
        ),
        size: widget.size,
      ),
    );
  }
}

class CenterLineWaveformPainter extends CustomPainter {
  final List<double> recordedWaveData;
  final Color waveColor;
  final Color liveWaveColor;
  final Color centerLineColor;
  final bool isRecording;
  final double scaleFactor;

  CenterLineWaveformPainter({
    required this.recordedWaveData,
    required this.waveColor,
    required this.liveWaveColor,
    required this.centerLineColor,
    required this.isRecording,
    required this.scaleFactor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final centerY = size.height / 2;
    final centerX = size.width / 2;

    // 绘制中心线
    final centerLinePaint = Paint()
      ..color = waveColor // 使用与左侧波形相同的颜色
      ..strokeWidth = 1.0; // 更细的中心线

    canvas.drawLine(
      Offset(centerX, 0),
      Offset(centerX, size.height),
      centerLinePaint,
    );

    // 绘制左侧已录制的波形（动态）
    _drawRecordedWaveform(canvas, size, centerX, centerY);

    // 绘制右侧固定波形（固定）
    _drawLiveWaveform(canvas, size, centerX, centerY);
  }

  void _drawRecordedWaveform(
      Canvas canvas, Size size, double centerX, double centerY) {
    if (recordedWaveData.isEmpty) return;

    final paint = Paint()
      ..color = waveColor
      ..strokeWidth = 1.5 // 更细的波形条
      ..strokeCap = StrokeCap.round;

    final spacing = 2.0; // 更小的间隔
    final maxBars = (centerX / (spacing + 1.5)).floor();
    final startIndex = math.max(0, recordedWaveData.length - maxBars);
    final visibleData = recordedWaveData.sublist(startIndex);

    // 从中心线向左绘制已录制的波形
    for (int i = 0; i < visibleData.length; i++) {
      final x = centerX - (i + 1) * (spacing + 1.5);
      if (x < 0) break;

      final amplitude = visibleData[visibleData.length - 1 - i] * scaleFactor;

      canvas.drawLine(
        Offset(x, centerY - amplitude),
        Offset(x, centerY + amplitude),
        paint,
      );
    }
  }

  void _drawLiveWaveform(
      Canvas canvas, Size size, double centerX, double centerY) {
    final paint = Paint()
      ..color = Colors.grey.shade300 // 浅灰色
      ..strokeWidth = 1.5 // 更细的波形条
      ..strokeCap = StrokeCap.round;

    final spacing = 2.0; // 更小的间隔
    final maxBars = ((size.width - centerX) / (spacing + 1.5)).floor();

    // 从中心线向右绘制固定幅值的波形
    for (int i = 0; i < maxBars; i++) {
      final x = centerX + (i + 1) * (spacing + 1.5);
      if (x > size.width) break;

      final amplitude = 0.2 * scaleFactor / 3; // 幅值减小3倍

      canvas.drawLine(
        Offset(x, centerY - amplitude),
        Offset(x, centerY + amplitude),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}
