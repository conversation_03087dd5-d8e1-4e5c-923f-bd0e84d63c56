import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';

class CustomTimePickerDialog extends StatefulWidget {
  final TimeOfDay initialStartTime;
  final TimeOfDay initialEndTime;
  final Function(TimeOfDay, TimeOfDay) onTimeSelected;

  const CustomTimePickerDialog({
    Key? key,
    required this.initialStartTime,
    required this.initialEndTime,
    required this.onTimeSelected,
  }) : super(key: key);

  @override
  _CustomTimePickerDialogState createState() => _CustomTimePickerDialogState();
}

class _CustomTimePickerDialogState extends State<CustomTimePickerDialog> {
  late TimeOfDay _startTime;
  late TimeOfDay _endTime;

  @override
  void initState() {
    super.initState();
    _startTime = widget.initialStartTime;
    _endTime = widget.initialEndTime;
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '设置夜间休眠时间',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 20),
            Row(
              children: [
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        '开始时间',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                      SizedBox(height: 10),
                      _buildTimePicker(
                        initialTime: _startTime,
                        onTimeChanged: (newTime) {
                          setState(() {
                            _startTime = newTime;
                          });
                        },
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 20),
                Expanded(
                  child: Column(
                    children: [
                      Text(
                        '结束时间',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[700],
                        ),
                      ),
                      SizedBox(height: 10),
                      _buildTimePicker(
                        initialTime: _endTime,
                        onTimeChanged: (newTime) {
                          setState(() {
                            _endTime = newTime;
                          });
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: Text('取消'),
                ),
                SizedBox(width: 10),
                ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                  ),
                  onPressed: () {
                    widget.onTimeSelected(_startTime, _endTime);
                    Navigator.pop(context);
                  },
                  child: Text('确定'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimePicker({
    required TimeOfDay initialTime,
    required Function(TimeOfDay) onTimeChanged,
  }) {
    // 创建小时和分钟数据源
    final List<int> hours = List.generate(24, (index) => index);
    final List<int> minutes = List.generate(60, (index) => index);

    // 初始滚动位置
    final int initialHour = initialTime.hour;
    final int initialMinute = initialTime.minute;

    return Container(
      height: 150,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          // 小时选择器
          Expanded(
            child: CupertinoPicker(
              itemExtent: 40,
              scrollController: FixedExtentScrollController(
                initialItem: initialHour,
              ),
              onSelectedItemChanged: (int index) {
                onTimeChanged(
                  TimeOfDay(hour: index, minute: initialTime.minute),
                );
              },
              children: hours.map((hour) {
                return Center(
                  child: Text(
                    hour.toString().padLeft(2, '0'),
                    style: TextStyle(fontSize: 20),
                  ),
                );
              }).toList(),
            ),
          ),
          Text(
            ':',
            style: TextStyle(fontSize: 20),
          ),
          // 分钟选择器
          Expanded(
            child: CupertinoPicker(
              itemExtent: 40,
              scrollController: FixedExtentScrollController(
                initialItem: initialMinute,
              ),
              onSelectedItemChanged: (int index) {
                onTimeChanged(
                  TimeOfDay(hour: initialTime.hour, minute: index),
                );
              },
              children: minutes.map((minute) {
                return Center(
                  child: Text(
                    minute.toString().padLeft(2, '0'),
                    style: TextStyle(fontSize: 20),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }
}
