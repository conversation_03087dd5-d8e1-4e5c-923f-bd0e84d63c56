import 'package:vibration/vibration.dart';

import '../utils/app_logger.dart';

class VibrationService {
  // 检查设备是否支持振动
  Future<bool> hasVibrator() async {
    try {
      return await Vibration.hasVibrator();
    } catch (e, stackTrace) {
      AppLogger.error('检查振动器失败：$e', error: e, stackTrace: stackTrace);
      return false;
    }
  }

  // 执行振动模式
  Future<void> vibrate() async {
    try {
      bool hasVibrator = await this.hasVibrator();
      if (hasVibrator) {
        // 创建一个报警模式的振动序列 - 交替短振动和暂停
        await Vibration.vibrate(
          pattern: [500, 1000, 500, 1000, 500, 1000],
          intensities: [128, 255, 128, 255, 128, 255],
          repeat: 0, // 设置为-1表示无限重复，这里设为0表示重复一次
        );
      }
    } catch (e, stackTrace) {
      AppLogger.error('执行振动失败：$e', error: e, stackTrace: stackTrace);
    }
  }

  // 停止振动
  Future<void> stopVibration() async {
    try {
      await Vibration.cancel();
    } catch (e, stackTrace) {
      AppLogger.error('停止振动失败：$e', error: e, stackTrace: stackTrace);
    }
  }
}
