import 'package:flutter/material.dart';
import '../models/pet.dart';
import '../constants/constants.dart';
import '../utils/toast_utils.dart';
import '../utils/app_logger.dart';
import '../widgets/pet_header_image.dart';

class PetDetailScreen extends StatefulWidget {
  final Pet pet;

  const PetDetailScreen({Key? key, required this.pet}) : super(key: key);

  @override
  State<PetDetailScreen> createState() => _PetDetailScreenState();
}

class _PetDetailScreenState extends State<PetDetailScreen> {
  late Pet _pet;

  @override
  void initState() {
    super.initState();
    _pet = widget.pet;
  }

  Widget _buildHeaderSection() {
    return PetHeaderImage(selectedSpecies: _pet.species);
  }

  Widget _buildInfoCard(String title, List<Widget> children) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value,
      {IconData? icon, bool isLast = false}) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            children: [
              if (icon != null) ...[
                Icon(icon, size: 20, color: Colors.grey[600]),
                const SizedBox(width: 12),
              ],
              Expanded(
                flex: 2,
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[700],
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Expanded(
                flex: 3,
                child: Text(
                  value,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.black87,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
        if (!isLast)
          Divider(
            color: Colors.grey.shade300,
            height: 1,
            thickness: 0.5,
          ),
      ],
    );
  }

  Widget _buildBasicInfo() {
    return _buildInfoCard(
      '基本信息',
      [
        _buildInfoRow('昵称', _pet.nickname, icon: Icons.favorite),
        _buildInfoRow('种类', _pet.species.displayName, icon: Icons.pets),
        _buildInfoRow('品种', _pet.breed, icon: Icons.category),
        _buildInfoRow('体型', _pet.size.displayName, icon: Icons.straighten),
        _buildInfoRow(
          '生日',
          '${_pet.birthday.year}年${_pet.birthday.month}月${_pet.birthday.day}日',
          icon: Icons.cake,
        ),
        _buildInfoRow('年龄', _pet.ageDescription,
            icon: Icons.access_time, isLast: true),
      ],
    );
  }

  Widget _buildPhysicalInfo() {
    return _buildInfoCard(
      '体征信息',
      [
        _buildInfoRow('肩高', '${_pet.shoulderHeight} cm', icon: Icons.height),
        _buildInfoRow('体重', '${_pet.weight} kg',
            icon: Icons.monitor_weight, isLast: true),
      ],
    );
  }

  void _editPet() async {
    final result = await Navigator.of(context).pushNamed(
      Routes.petInfoInput,
      arguments: _pet,
    );

    if (result == true) {
      // 重新加载宠物信息
      final updatedPet = await Pet.getPet();
      if (updatedPet != null && mounted) {
        setState(() {
          _pet = updatedPet;
        });
      }
    }
  }

  void _showDeleteConfirmation() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('确认删除'),
          content: Text('确定要删除 ${_pet.nickname} 的信息吗？\n此操作不可恢复。'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                _deletePet();
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: const Text('删除'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _deletePet() async {
    try {
      await Pet.deletePet();
      // TODO: 调用API删除服务器上的数据
      // await _deleteFromServer();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('爱宠信息已删除'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );

        // 返回到上一页并刷新
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      AppLogger.error('删除宠物信息失败', error: e);
      ToastUtils.showError('删除失败，请重试');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('爱宠详情'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.orange.shade300,
        foregroundColor: Colors.white,
        actions: [
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  _editPet();
                  break;
                case 'delete':
                  _showDeleteConfirmation();
                  break;
              }
            },
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10.0),
            ),
            padding: EdgeInsets.symmetric(horizontal: 2),
            itemBuilder: (BuildContext context) => [
              PopupMenuItem<String>(
                value: 'edit',
                height: 30,
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 20, color: Colors.black),
                    SizedBox(width: 8),
                    Text('编辑'),
                  ],
                ),
              ),
              PopupMenuItem<String>(
                enabled: false, // 不可点击
                height: 1, // 可以给一个很小的高度，使其更紧凑
                child: Divider(
                  color: Colors.grey.shade400,
                  thickness: 0.5,
                ),
              ),
              PopupMenuItem<String>(
                value: 'delete',
                height: 30,
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 20, color: Colors.red),
                    SizedBox(width: 8),
                    Text('删除', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildHeaderSection(),
            const SizedBox(height: 16),
            _buildBasicInfo(),
            _buildPhysicalInfo(),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
