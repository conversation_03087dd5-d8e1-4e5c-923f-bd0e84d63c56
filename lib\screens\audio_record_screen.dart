import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/device_provider.dart';
import '../services/device_service.dart';
import '../services/audio_recorder_service.dart';
import '../widgets/animated_time_display.dart';
import '../widgets/custom_waveform_widget.dart';
import '../widgets/custom_player_waveform_widget.dart';

class AudioRecordScreen extends StatefulWidget {
  const AudioRecordScreen({Key? key}) : super(key: key);

  @override
  _AudioRecordScreenState createState() => _AudioRecordScreenState();
}

class _AudioRecordScreenState extends State<AudioRecordScreen> {
  AudioRecorderService _audioService = AudioRecorderService();
  Duration _lastPlaybackTime = Duration.zero; // 记录上次播放时间，用于暂停时保持位置
  bool _isInitializing = true; // 添加初始化状态标记

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await _initializeAudio();
    });
  }

  Future<void> _initializeAudio() async {
    // 设置状态变化回调
    _audioService.setStateChangeCallback(() {
      if (mounted) {
        setState(() {
          // 当播放完成时（音频时间被重置为"00:00"且不在播放状态），重置播放时间
          if (!_audioService.isPlaying && _audioService.audioTime == "00:00") {
            _lastPlaybackTime = Duration.zero;
          }
        });
      }
    });

    // 初始化音频服务
    await _audioService.init();
    await Future.delayed(const Duration(seconds: 1));
    // 初始化完成，更新UI状态
    if (mounted) {
      setState(() {
        _isInitializing = false;
      });
    }
  }

  @override
  void dispose() {
    _audioService.dispose();
    super.dispose();
  }

  void _startRecording() {
    _audioService.startRecording();
  }

  void _stopRecording() {
    _audioService.stopRecording();
  }

  void _playRecording() {
    _audioService.playRecording();
  }

  void _stopPlayback() {
    _audioService.stopPlayback();
  }

  void _resetRecording() {
    _lastPlaybackTime = Duration.zero; // 重录时重置播放时间
    _audioService.deleteRecording();
  }

  void _useRecording() async {
    final deviceService = Provider.of<DeviceApiService>(context, listen: false);
    final device = Provider.of<DeviceProvider>(context, listen: false).device;
    // 上传录音文件到服务器
    var result = await deviceService.deviceAudioUpload(
        device!.deviceName, _audioService.recordingPath);
    if (result) {
      // 发送命令更新设备的录音文件
      result = await deviceService.deviceAudioUpdate(device.deviceName);
      if (result) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('录音已采用，设备正在下载中....')),
        );
      } else {
        // 上传失败，删除录音文件
        await _audioService.deleteRecording();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('录音'),
      ),
      body: _isInitializing
          ? Center(
              child: CircularProgressIndicator(), // 初始化时显示加载指示器
            )
          : Column(
              children: [
                // 上部：录音/播放时间显示（字符级动画更新）
                Expanded(
                  flex: 2,
                  child: Center(
                    child: ValueListenableBuilder<String>(
                      valueListenable: _audioService.audioTimeNotifier,
                      builder: (context, timeValue, child) {
                        return AnimatedTimeDisplay(
                          timeString: timeValue,
                          textStyle: TextStyle(
                            fontSize: 48,
                            fontWeight: FontWeight.bold,
                          ),
                          animationDuration:
                              Duration(milliseconds: 300), // 适中的动画时长，避免模糊
                        );
                      },
                    ),
                  ),
                ),

                // 中部：波形显示
                Expanded(
                  flex: 6,
                  child: Center(
                    child: _buildWaveform(),
                  ),
                ),

                // 下部：按钮区域
                Expanded(
                  flex: 2,
                  child: Center(
                    child: _buildControlButtons(),
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildWaveform() {
    // 播放状态下显示播放波形
    if (_audioService.hasRecorded) {
      // 使用自定义播放波形组件（左侧已播放，右侧原始音频波形）
      return ValueListenableBuilder<String>(
        valueListenable: _audioService.audioTimeNotifier,
        builder: (context, timeValue, child) {
          // 解析当前播放时间
          Duration currentTime = Duration.zero;

          if (_audioService.isPlaying) {
            // 播放状态下解析当前播放时间
            try {
              final parts = timeValue.split(':');
              if (parts.length == 2) {
                final seconds = int.parse(parts[0]);
                final centiseconds = int.parse(parts[1]);
                currentTime =
                    Duration(milliseconds: seconds * 1000 + centiseconds * 10);
                // 更新最后播放时间
                _lastPlaybackTime = currentTime;
              }
            } catch (e) {
              // 解析失败时使用上次记录的时间
              currentTime = _lastPlaybackTime;
            }
          } else if (_audioService.hasRecorded) {
            // 暂停状态下使用上次记录的播放时间，保持波形位置
            currentTime = _lastPlaybackTime;
          } else {
            // 未开始播放时重置时间
            currentTime = Duration.zero;
            _lastPlaybackTime = Duration.zero;
          }

          // 获取总时长
          final totalDuration =
              Duration(milliseconds: _audioService.totalRecordingDuration);

          return CustomPlayerWaveformWidget(
            waveformData: _audioService.playerController.waveformData,
            currentTime: currentTime,
            totalDuration: totalDuration,
            height: 160,
            playedColor: Colors.red, // 左侧已播放波形颜色
            unplayedColor: const Color(0xFFBBBBBB), // 右侧未播放波形改为浅灰色
            centerLineColor: Colors.blue, // 中心线颜色
            scaleFactor: 200, // 调整为与原始AudioFileWaveforms一致的缩放倍数
          );
        },
      );
    }

    // 录音状态下或初始状态显示录音波形
    // 使用自定义波形组件（中心线分割，左侧固定波形，右侧实时波形）
    return CustomWaveformWidget(
      waveData: _audioService.recorderController.waveData,
      size: Size(MediaQuery.of(context).size.width, 160),
      waveColor: Colors.blue, // 左侧已录制波形颜色
      liveWaveColor: Colors.red, // 右侧实时波形颜色
      centerLineColor: Colors.grey, // 中心线颜色
      isRecording: _audioService.isRecording,
      scaleFactor: 70,
    );
  }

  Widget _buildControlButtons() {
    // 录制完成后显示的三个按钮
    if (_audioService.hasRecorded) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // 重录按钮
          IconButton(
            onPressed: _resetRecording,
            icon: Icon(Icons.refresh, size: 30),
            tooltip: '重录',
          ),

          // 试听按钮
          IconButton(
            onPressed: _audioService.isPlaying ? _stopPlayback : _playRecording,
            icon: Icon(
              _audioService.isPlaying ? Icons.stop : Icons.play_arrow,
              size: 50,
              color: Colors.blue,
            ),
            tooltip: _audioService.isPlaying ? '停止' : '播放',
          ),

          // 采用按钮
          IconButton(
            onPressed: _useRecording,
            icon: Icon(Icons.check_circle, size: 30, color: Colors.green),
            tooltip: '采用',
          ),
        ],
      );
    }

    // 录制前或录制中的单一按钮
    return IconButton(
      onPressed: _audioService.isRecording ? _stopRecording : _startRecording,
      icon: Icon(
        _audioService.isRecording ? Icons.stop_circle : Icons.mic,
        size: 70,
        color: _audioService.isRecording ? Colors.red : Colors.blue,
      ),
      tooltip: _audioService.isRecording ? '停止录音' : '开始录音',
    );
  }
}
