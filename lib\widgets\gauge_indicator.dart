import 'package:flutter/material.dart';

class GaugeIndicator extends StatelessWidget {
  final double value; // 0到1之间的值，表示刻度位置
  final List<Color> colors;

  const GaugeIndicator({
    Key? key,
    required this.value,
    this.colors = const [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.red,
      Colors.purple,
      Colors.pink,
    ],
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 20,
      child: Stack(
        children: [
          // 彩色刻度条
          Container(
            height: 8,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4),
              gradient: LinearGradient(
                colors: colors,
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
            ),
          ),

          // 指示器
          Positioned(
            left: value.clamp(0.0, 1.0) *
                (MediaQuery.of(context).size.width - 50), // 减去padding
            top: 0,
            child: Container(
              height: 20,
              width: 8,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(4),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black26,
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
