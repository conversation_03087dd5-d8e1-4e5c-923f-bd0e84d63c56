import 'package:flutter/material.dart';

class ActivityChart extends StatelessWidget {
  final int walking;
  final int running;
  final int jumping;

  const ActivityChart({
    Key? key,
    required this.walking,
    required this.running,
    required this.jumping,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 计算最大值，用于设置柱状图的高度比例
    final maxValue = [walking, running, jumping]
        .reduce((curr, next) => curr > next ? curr : next);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 图表
        Expanded(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildBar('走路', walking, maxValue, Colors.blue),
              _buildBar('奔跑', running, maxValue, Colors.orange),
              _buildBar('跳跃', jumping, maxValue, Colors.green),
            ],
          ),
        ),
        // X轴
        Container(
          height: 1,
          color: Colors.grey[300],
          margin: EdgeInsets.only(top: 8),
        ),
        // X轴标签
        Padding(
          padding: EdgeInsets.only(top: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Text('走路', style: TextStyle(fontSize: 12, color: Colors.blue)),
              Text('奔跑', style: TextStyle(fontSize: 12, color: Colors.orange)),
              Text('跳跃', style: TextStyle(fontSize: 12, color: Colors.green)),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBar(String label, int value, int maxValue, Color color) {
    final double percentage = maxValue > 0 ? value / maxValue : 0;

    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        // 数值标签
        Text(
          value.toString(),
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        SizedBox(height: 4),
        // 柱状图
        Container(
          width: 40,
          height: 150 * percentage,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.vertical(top: Radius.circular(4)),
          ),
        ),
      ],
    );
  }
}
