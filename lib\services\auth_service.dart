import '../services/api_service.dart';
import '../utils/app_logger.dart';
import '../constants/url_form.dart';

/// 用户登录、退出、获取验证码服务
class AuthService {
  final ApiService _apiService;

  AuthService(this._apiService);

  Future<Map<String, dynamic>> login(String phone, String smsCode) async {
    final response = await _apiService.postRequest(
      UrlFormat.AuthLogin,
      {'phone': phone, 'code': smsCode},
      useToken: false,
    );
    final code = response['code'];
    if (code != 200) {
      throw Exception(response['message']);
    }
    return response['data'];
  }

  Future<void> logout() async {
    try {
      await _apiService.postRequest(UrlFormat.AuthLogout, {});
    } catch (e, stackTrace) {
      AppLogger.error('退出登录失败：$e', error: e, stackTrace: stackTrace);
      rethrow;
    }
  }

  Future<void> getSmsCode(String phone) async {
    // 这个接口是发送短信验证码的接口不需要携带token
    final response = await _apiService.postRequest(
      UrlFormat.AuthSmsCode,
      {'phone': phone},
      useToken: false,
    );
    final code = response['code'];
    if (code != 200) {
      throw Exception(response['message']);
    }
  }

  //刷新token，在api接口拦截异常返回，一旦异常则退出登录
  Future<Map<String, dynamic>> refreshToken(
      String phone, String refreshToken) async {
    final response = await _apiService.postRequest(
      UrlFormat.AuthRefreshToken,
      {'phone': phone, 'refresh_token': refreshToken},
      useToken: false,
    );
    if (response['code'] != 200) {
      throw Exception(response['message']);
    }
    return response['data'];
  }
}
